import sys
import os
import fitz

def extract(pdf_path, output_dir):
    doc = fitz.open(pdf_path)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    img_dir = os.path.join(output_dir, "images")
    if not os.path.exists(img_dir):
        os.makedirs(img_dir)
    md_path = os.path.join(
        output_dir,
        os.path.splitext(os.path.basename(pdf_path))[0] + ".md"
    )
    with open(md_path, "w", encoding="utf-8") as md_file:
        for page_num in range(len(doc)):
            page = doc[page_num]
            blocks = page.get_text("dict")["blocks"]
            # 定义顶部和底部边距，忽略页眉页脚
            top_margin = 50
            bottom_margin = page.rect.height - 50
            # 提取正文文本
            for b in blocks:
                if b["type"] == 0:
                    bbox = b["bbox"]
                    if bbox[1] < top_margin or bbox[3] > bottom_margin:
                        continue
                    for line in b["lines"]:
                        span_text = "".join(span["text"] for span in line["spans"])
                        md_file.write(span_text + "\n")
            # 提取图片
            image_list = page.get_images(full=True)
            for img_index, img in enumerate(image_list, start=1):
                xref = img[0]
                pix = fitz.Pixmap(doc, xref)
                img_name = f"page_{page_num+1}_img_{img_index}.png"
                img_path = os.path.join(img_dir, img_name)
                if pix.n < 5:
                    pix.save(img_path)
                else:
                    pix0 = fitz.Pixmap(fitz.csRGB, pix)
                    pix0.save(img_path)
                    pix0 = None
                pix = None
                md_file.write(f"![{img_name}](images/{img_name})\n")
    print(f"Markdown and images have been saved to '{output_dir}'")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python extract_pdf_md.py <pdf_path> [output_dir]")
        sys.exit(1)
    pdf_path = sys.argv[1]
    out_dir = sys.argv[2] if len(sys.argv) >= 3 else os.path.splitext(pdf_path)[0] + "_md"
    extract(pdf_path, out_dir)