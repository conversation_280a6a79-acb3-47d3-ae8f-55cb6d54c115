# PDF图片位置优化功能说明

## 功能概述

本优化版本的PDF提取器实现了基于边界框（Bounding Box）分析的智能图片定位功能，可以：

1. **精确获取位置信息**：提取PDF中每个图片和文本块的边界框坐标
2. **智能分析位置关系**：通过计算边界框距离，判断图片与文本的相对位置
3. **合理插入图片**：根据位置关系，将图片插入到Markdown中的最合适位置

## 技术原理

### 边界框坐标系统
每个元素的边界框由四个坐标定义：`(x0, y0, x1, y1)`
- `(x0, y0)`: 左下角坐标
- `(x1, y1)`: 右上角坐标

### 位置关系分析

1. **水平重叠检测**
   ```python
   x_overlap = not (img_bbox[2] < text_bbox[0] or img_bbox[0] > text_bbox[2])
   ```

2. **垂直位置关系**
   - 文本在图片上方：`text_bbox[3] <= img_bbox[1]`
   - 文本在图片下方：`text_bbox[1] >= img_bbox[3]`
   - 文本与图片重叠：其他情况

3. **距离计算**
   - 上方距离：`img_bbox[1] - text_bbox[3]`
   - 下方距离：`text_bbox[1] - img_bbox[3]`

## 位置关系类型

### 1. between_paragraphs (段落间)
- **条件**：图片同时有上方和下方的文本，且距离都很近（< 50像素）
- **处理**：在上方文本后插入图片
- **示例**：
  ```
  这是第一段文字。
  
  [图片位置]
  
  这是第二段文字。
  ```

### 2. after_paragraph (段落后)
- **条件**：图片主要位于某段文字下方
- **处理**：在该段文字后插入图片
- **示例**：
  ```
  这是一段文字的说明。
  
  [图片位置]
  ```

### 3. before_paragraph (段落前)
- **条件**：图片主要位于某段文字上方
- **处理**：在该段文字前插入图片
- **示例**：
  ```
  [图片位置]
  
  这是对上面图片的说明。
  ```

### 4. inline (内嵌)
- **条件**：图片与文字在垂直方向有重叠
- **处理**：在重叠文字后立即插入图片
- **示例**：
  ```
  这段文字中包含图片[图片位置]继续的文字。
  ```

### 5. standalone (独立)
- **条件**：图片周围没有足够近的文字
- **处理**：添加到页面末尾或根据垂直位置插入
- **示例**：
  ```
  [独立的图片]
  ```

## 核心算法流程

```python
def _analyze_image_text_relationships(self, text_blocks, image_blocks):
    for img_block in image_blocks:
        # 1. 获取图片边界框
        img_bbox = img_block['bbox']
        
        # 2. 遍历所有文本块
        for text_block in text_blocks:
            text_bbox = text_block['bbox']
            
            # 3. 检查水平重叠
            if x_overlap:
                # 4. 计算垂直距离
                if text_above_image:
                    closest_above = min_distance_text
                elif text_below_image:
                    closest_below = min_distance_text
                else:
                    overlapping_text.append(text)
        
        # 5. 根据距离和位置确定插入策略
        position_type = determine_position_type(
            closest_above, closest_below, overlapping_text
        )
```

## 使用方法

### 1. 基本使用
```python
from pdf_extractor import PDFExtractor
from file_manager import FileManager

# 创建实例
file_manager = FileManager("output")
extractor = PDFExtractor(file_manager)

# 提取PDF（自动使用智能位置分析）
result = extractor.extract_from_pdf("document.pdf")
```

### 2. 测试新功能
```bash
# 运行测试脚本
python test_image_positioning.py document.pdf

# 指定输出目录
python test_image_positioning.py document.pdf custom_output
```

### 3. 查看详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
# 然后运行提取，会看到详细的位置分析信息
```

## 参数调整

### 距离阈值
可以通过修改代码中的阈值来调整位置判断的敏感度：

```python
# 在 _analyze_image_text_relationships 方法中
if closest_above_distance < 50 and closest_below_distance < 50:
    # 调整这个50像素的阈值
```

### 边距设置
```python
# 提取时设置页面边距
result = extractor.extract_from_pdf(
    pdf_path="document.pdf",
    top_margin=60,    # 顶部边距
    bottom_margin=60  # 底部边距
)
```

## 优化效果

### 优化前
- 图片统一放在页面文本后面
- 无法反映图片与文本的真实关系
- 阅读体验不连贯

### 优化后
- 图片根据实际位置智能插入
- 保持原始文档的视觉逻辑
- 提供更自然的阅读体验

## 日志信息示例

```
2024-06-14 10:30:15 [DEBUG] 保存图片: doc_page1_img1.png, 位置类型: between_paragraphs
2024-06-14 10:30:15 [DEBUG] 保存图片: doc_page1_img2.png, 位置类型: after_paragraph  
2024-06-14 10:30:15 [DEBUG] 保存图片: doc_page2_img1.png, 位置类型: inline
```

## 注意事项

1. **坐标系统**：PyMuPDF使用左下角为原点的坐标系统
2. **边距处理**：图片和文本都会过滤掉页眉页脚区域
3. **重叠检测**：只考虑水平方向的重叠来判断相关性
4. **距离计算**：使用边界框边缘距离，不是中心点距离

## 扩展功能

可以进一步扩展的功能：
- 支持图片标题识别
- 表格与图片的关系分析
- 图片尺寸优化建议
- 多列文档的复杂布局处理
