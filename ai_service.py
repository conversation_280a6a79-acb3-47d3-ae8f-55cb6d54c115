#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ai_service.py

AI 服务模块，提供统一的AI调用接口和多通道支持
"""

import json
import time
import logging
import random
import threading
from pathlib import Path
from typing import Dict, Tuple, List, Optional
from dataclasses import dataclass
from enum import Enum
import requests
from requests.exceptions import RequestException, Timeout


class ChannelStatus(Enum):
    """通道状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    DISABLED = "disabled"


@dataclass
class ChannelConfig:
    """AI通道配置"""
    name: str
    api_base_url: str
    api_provider: str
    api_key: str
    model_name: str
    request_timeout: int = 120
    weight: int = 1
    enabled: bool = True
    max_concurrent: int = 5
    use_streaming: bool = True  # 是否使用流式响应


@dataclass
class ChannelStats:
    """通道统计信息"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_response_time: float = 0.0
    last_request_time: Optional[float] = None
    status: ChannelStatus = ChannelStatus.ACTIVE
    error_message: Optional[str] = None

    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests

    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        if self.successful_requests == 0:
            return 0.0
        return self.total_response_time / self.successful_requests


class AIService:
    """AI 服务类，支持多通道和负载均衡"""

    def __init__(self, config: Dict):
        """初始化AI服务

        Args:
            config: 配置字典，包含通道配置或单通道配置
        """
        self.config = config
        self.system_prompt, self.task_prompt = self._create_ai_prompt()

        # 解析通道配置
        self.channels = self._parse_channel_config(config)
        self.stats = {ch.name: ChannelStats() for ch in self.channels}
        self.channel_locks = {ch.name: threading.Semaphore(ch.max_concurrent) for ch in self.channels}
        self._current_channel_index = 0
        self._lock = threading.Lock()

        logging.info(f"AI服务初始化完成，共 {len(self.channels)} 个通道")

    def _parse_channel_config(self, config: Dict) -> List[ChannelConfig]:
        """解析通道配置"""
        channels = []

        # 检查是否有多通道配置
        if 'channels' in config and config['channels']:
            # 新的多通道配置格式
            for channel_data in config['channels']:
                if channel_data.get('enabled', True):
                    channels.append(ChannelConfig(**channel_data))
        else:
            # 向后兼容：单通道配置
            if all(key in config for key in ['api_base_url', 'api_key', 'model_name']):
                channels.append(ChannelConfig(
                    name="default",
                    api_base_url=config['api_base_url'],
                    api_provider=config.get('api_provider', 'openai'),
                    api_key=config['api_key'],
                    model_name=config['model_name'],
                    request_timeout=config.get('request_timeout', 120),
                    weight=1,
                    enabled=True,
                    max_concurrent=5,
                    use_streaming=config.get('use_streaming', True)
                ))

        return channels

    def _create_ai_prompt(self) -> Tuple[str, str]:
        """创建 AI 指令"""
        system_prompt = (
            "你是一个精通数据提取的AI助手，专注于从结构化和非结构化的文本中准确解析教育内容。"
            "你的任务是从给定的文本中提取完整的问答对，并严格按照指定格式返回。"
            "特别注意：你必须确保所有图片信息都被正确提取和保留，包括完整的文件路径。"
        )
        
        task_prompt = """请严格按照以下规则，分析提供的文本片段，并提取其中所有完整的'问题-答案'对：

**提取规则：**
1. **完整性优先：** 如果文本片段末尾的问题或答案不完整，必须彻底忽略该条目，绝不猜测或补全。

2. **格式保留：** front（问题）和 back（答案）字段的内容应尽量保留原始格式，如列表、代码块等。对于换行，请直接保留 \\n。

3. **图片处理（重要）：**
   - 若内容中包含HTML图片标签格式 <img alt="..." src="文件路径">，则必须在输出中保留完整的图片引用。
   - 完整保留图片的src属性中的文件路径，包括文件夹路径，如 path/to/image.png、folder\\subfolder\\image.svg 等。
   - 保持原始的HTML img标签格式不变。
   - **智能图片归属判断：** 根据图片在原文中的上下文位置，智能判断图片应该归属于问题部分（front字段）还是答案部分（back字段）：
     * 如果图片出现在题目描述、选项列表或问题相关内容中，则将图片放入 front 字段
     * 如果图片出现在答案解析、解题步骤或答案说明中，则将图片放入 back 字段
     * 允许调整图片在front/back字段中的位置，但必须保持图片与相关文字内容的逻辑关联
   - 绝对不能遗漏任何图片信息，无论图片位于文件系统的哪个位置。
   - **注意：** 只允许移动图片位置到合适的字段，严禁修改任何文字内容。

4. **内容忽略：** 在提取过程中，必须忽略所有出现的无用营销信息，特别是 "专业笔试助攻\\代做:jobhelp101" 等，以及 "## 第 3 页" 这种页数信息。

**输出格式要求：**
你必须将所有提取出的'问题-答案'对整合到一个单一的JSON对象中。此JSON对象必须包含一个根键 "questions"，其值为一个JSON对象列表。列表中的每个对象代表一道独立的题目，且必须包含 "front" 和 "back" 两个键。

注意需要保留题目中本来有的序号。
如果当前文本块中没有发现任何完整的问答对，则返回 {"questions": []}。

**JSON 格式示例（注意图片智能归属）：**
{
  "questions": [
    {
      "front": "1. <img alt=\"电路图\" src=\"circuits/wye-delta.svg\">\\n根据上图所示的电路连接方式，这种配置被称为？\\n- A. 星形连接\\n- B. 三角形连接\\n- C. 串联连接\\n- D. 并联连接",
      "back": "答案：B. 三角形连接\\n\\n解析：从图中可以看出这是典型的三角形（Delta）连接方式，三个绕组首尾相连形成闭合回路。"
    },
    {
      "front": "2. 计算下列积分的值：∫(x² + 2x)dx\\n- A. x³/3 + x² + C\\n- B. x³/3 + 2x² + C\\n- C. x³ + x² + C\\n- D. 2x³ + x² + C",
      "back": "答案：A. x³/3 + x² + C\\n\\n解析：<img alt=\"积分步骤\" src=\"math/integration_steps.png\">\\n按照幂函数积分公式逐步计算，如上图所示的详细步骤。"
    },
    {
      "front": "3. <img alt=\"几何图形\" src=\"geometry/triangle_abc.png\">\\n在上图三角形ABC中，如果∠A=60°，AB=8cm，AC=6cm，求三角形的面积。",
      "back": "答案：12√3 cm²\\n\\n解析：使用面积公式 S = (1/2)ab·sinC\\n<img alt=\"计算过程\" src=\"geometry/area_calculation.png\">\\n代入数值计算得到最终结果。"
    }
  ]
}

**重要提醒：**
- 必须确保所有出现在文本中的 <img> 标签都被完整保留在输出中
- 完整保留src属性中的文件路径，包括文件夹结构（可能使用 / 或 \\ 分隔符）
- 保持HTML img标签的原始格式不变
- **智能判断图片归属：** 根据图片的上下文内容，将图片放置在最合适的字段中（front或back）
- **图片与题目关联：** 问题相关的图片放在front字段，答案解析相关的图片放在back字段
- 允许调整图片位置，但严禁修改任何文字内容
- 绝对不能遗漏任何图片信息
- 只返回JSON格式的数据，不要包含任何其他文字说明"""
    
        return system_prompt, task_prompt

    def get_available_channels(self) -> List[str]:
        """获取可用的通道列表"""
        available = []
        for channel in self.channels:
            if (channel.enabled and
                self.stats[channel.name].status in [ChannelStatus.ACTIVE, ChannelStatus.INACTIVE]):
                available.append(channel.name)
        return available

    def select_channel(self, strategy: str = "weighted_round_robin") -> Optional[ChannelConfig]:
        """选择一个通道"""
        available_channels = self.get_available_channels()
        if not available_channels:
            return None

        if strategy == "weighted_round_robin":
            # 构建加权列表
            weighted_channels = []
            for channel in self.channels:
                if channel.name in available_channels:
                    weighted_channels.extend([channel] * channel.weight)

            if not weighted_channels:
                return None

            with self._lock:
                selected = weighted_channels[self._current_channel_index % len(weighted_channels)]
                self._current_channel_index += 1
                return selected

        # 默认返回第一个可用通道
        for channel in self.channels:
            if channel.name in available_channels:
                return channel

        return None

    def update_stats(self, channel_name: str, success: bool, response_time: float, error_msg: str = None):
        """更新通道统计信息"""
        if channel_name not in self.stats:
            return

        stats = self.stats[channel_name]
        stats.total_requests += 1
        stats.last_request_time = time.time()

        if success:
            stats.successful_requests += 1
            stats.total_response_time += response_time
            stats.status = ChannelStatus.ACTIVE
            stats.error_message = None
        else:
            stats.failed_requests += 1
            if error_msg:
                stats.error_message = error_msg

            # 如果连续失败过多，标记为错误状态
            if stats.success_rate < 0.5 and stats.total_requests >= 10:
                stats.status = ChannelStatus.ERROR

    def get_stats(self) -> Dict:
        """获取统计摘要"""
        summary = {
            'total_channels': len(self.channels),
            'active_channels': len([s for s in self.stats.values() if s.status == ChannelStatus.ACTIVE]),
            'channels': {}
        }

        for name, stats in self.stats.items():
            summary['channels'][name] = {
                'status': stats.status.value,
                'total_requests': stats.total_requests,
                'success_rate': round(stats.success_rate * 100, 2),
                'avg_response_time': round(stats.average_response_time, 2),
                'error_message': stats.error_message
            }

        return summary

    def test_channel(self, channel_name: str) -> Dict:
        """测试指定通道的连接性"""
        channel = None
        for ch in self.channels:
            if ch.name == channel_name:
                channel = ch
                break

        if not channel:
            return {
                'success': False,
                'error': f'通道 {channel_name} 不存在',
                'response_time': 0
            }

        if not channel.enabled:
            return {
                'success': False,
                'error': f'通道 {channel_name} 已禁用',
                'response_time': 0
            }

        # 构建测试请求
        test_text = "这是一个测试请求，请回复'测试成功'。"

        start_time = time.time()
        try:
            # 使用简单的测试调用
            success = self._test_single_channel(channel, test_text)
            response_time = time.time() - start_time

            if success:
                return {
                    'success': True,
                    'error': None,
                    'response_time': round(response_time, 2)
                }
            else:
                return {
                    'success': False,
                    'error': '测试请求失败',
                    'response_time': round(response_time, 2)
                }

        except Exception as e:
            response_time = time.time() - start_time
            return {
                'success': False,
                'error': str(e),
                'response_time': round(response_time, 2)
            }

    def _test_single_channel(self, channel: ChannelConfig, test_text: str) -> bool:
        """测试单个通道"""
        url = channel.api_base_url.rstrip("/") + "/chat/completions"
        headers = {
            "Authorization": f"Bearer {channel.api_key}",
            "Content-Type": "application/json",
        }

        # 构建简单的测试payload
        if channel.api_provider.lower() == "siliconflow":
            payload = {
                "model": channel.model_name,
                "messages": [{"role": "user", "content": test_text}],
                "temperature": 0.1,
                "max_tokens": 50,
                "stream": False,  # 测试时不使用流式
            }
        else:
            payload = {
                "model": channel.model_name,
                "messages": [
                    {"role": "system", "content": "你是一个AI助手，请简短回复。"},
                    {"role": "user", "content": test_text},
                ],
                "temperature": 0.1,
                "max_tokens": 50,
                "stream": False,  # 测试时不使用流式
            }

        try:
            resp = requests.post(
                url,
                headers=headers,
                json=payload,
                timeout=min(channel.request_timeout, 30),  # 测试时使用较短的超时
                stream=False
            )

            if resp.status_code == 200:
                # 检查响应内容
                response_data = resp.json()
                if 'choices' in response_data and len(response_data['choices']) > 0:
                    return True

            logging.warning(f"通道 {channel.name} 测试失败，状态码: {resp.status_code}")
            return False

        except Exception as e:
            logging.error(f"通道 {channel.name} 测试异常: {e}")
            return False

    def test_all_channels(self) -> Dict[str, Dict]:
        """测试所有通道"""
        results = {}
        for channel in self.channels:
            if channel.enabled:
                results[channel.name] = self.test_channel(channel.name)
            else:
                results[channel.name] = {
                    'success': False,
                    'error': '通道已禁用',
                    'response_time': 0
                }
        return results

    def _build_payload(self, chunk: str, channel: ChannelConfig) -> Dict:
        """构建API请求payload"""
        # 根据通道的 API Provider 动态构建 payload
        if channel.api_provider.lower() == "siliconflow":
            # SiliconFlow 使用单个用户消息
            full_prompt = f"{self.system_prompt}\n\n{self.task_prompt}\n\n---\n\n{chunk}"
            payload = {
                "model": channel.model_name,
                "messages": [{"role": "user", "content": full_prompt}],
                "temperature": 0.3,  # 降低温度以提高一致性
                "stream": channel.use_streaming,  # 根据通道配置决定是否流式
            }
        else:  # 默认为 openai 格式
            payload = {
                "model": channel.model_name,
                "messages": [
                    {"role": "system", "content": self.system_prompt + "\n" + self.task_prompt},
                    {"role": "user", "content": chunk},
                ],
                "temperature": 0.3,  # 降低温度以提高一致性
                "stream": channel.use_streaming,  # 根据通道配置决定是否流式
            }

        return payload
    
    def _collect_stream_response(self, response) -> str:
        """收集流式响应内容"""
        content_parts = []
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data = line[6:]                                
                    if data == '[DONE]':
                        break
                    try:
                        chunk_data = json.loads(data)
                        if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                            delta = chunk_data['choices'][0].get('delta', {})
                            # 检查 content 字段
                            if 'content' in delta and delta['content'] is not None:
                                content_parts.append(delta['content'])
                            # 同时检查 reasoning_content 字段（某些模型使用这个字段）
                            elif 'reasoning_content' in delta and delta['reasoning_content'] is not None:
                                content_parts.append(delta['reasoning_content'])
                    except json.JSONDecodeError:
                        continue
        
        return ''.join(content_parts).strip()

    def _collect_non_stream_response(self, response) -> str:
        """收集非流式响应内容"""
        try:
            response_data = response.json()
            if 'choices' in response_data and len(response_data['choices']) > 0:
                choice = response_data['choices'][0]
                if 'message' in choice and 'content' in choice['message']:
                    return choice['message']['content'].strip()
                elif 'text' in choice:  # 某些API使用text字段
                    return choice['text'].strip()
            return ""
        except Exception as e:
            logging.error(f"解析非流式响应失败: {e}")
            return ""

    def _clean_json_response(self, content: str, idx: int) -> str:
        """清理和修复JSON响应"""
        # 尝试清理响应内容（移除可能的markdown标记）
        if content.startswith("```json"):
            content = content[7:]
        if content.endswith("```"):
            content = content[:-3]
        content = content.strip()
        
        # 尝试修复可能不完整的 JSON
        if content and not content.endswith('}'):
            # 如果 JSON 不完整，尝试简单的修复
            if '"questions"' in content:
                # 计算已经开始的问题数量
                bracket_count = content.count('{') - content.count('}')
                
                # 简单的修复：添加缺失的右括号
                if bracket_count > 0:
                    content += '}' * bracket_count
                    logging.warning(f"块 {idx} 自动修复了不完整的 JSON 响应")
        
        return content
    
    def _parse_and_validate_response(self, content: str, idx: int) -> List[Dict[str, str]]:
        """解析和验证AI响应"""
        try:
            parsed = json.loads(content)
            questions = parsed.get("questions", [])
            
            # 验证问题格式
            valid_questions = []
            for q in questions:
                if isinstance(q, dict) and "front" in q and "back" in q:
                    if q["front"].strip() and q["back"].strip():
                        valid_questions.append(q)
            
            return valid_questions
            
        except json.JSONDecodeError as e:
            logging.error(f"块 {idx} JSON 解析失败: {e}")
            logging.error(f"原始响应内容: {content[:500]}...")
            
            # 尝试使用正则表达式提取部分内容
            import re
            pattern = r'"front":\s*"([^"]*(?:\\"[^"]*)*)"\s*,\s*"back":\s*"([^"]*(?:\\"[^"]*)*)"'
            matches = re.findall(pattern, content)
            
            if matches:
                logging.info(f"块 {idx} 通过正则表达式找到 {len(matches)} 个可能的问答对")
                recovered_questions = []
                for front, back in matches:
                    if front.strip() and back.strip():
                        recovered_questions.append({
                            "front": front.replace('\\"', '"'),
                            "back": back.replace('\\"', '"')
                        })
                
                return recovered_questions
            
            return []
    
    def call_api(self, chunk: str, idx: int, cache_dir: str = None) -> bool:
        """调用 AI API 提取问答对，支持多通道和负载均衡

        Args:
            chunk: 文本块内容
            idx: 块索引
            cache_dir: 缓存目录，如果为None则不保存缓存

        Returns:
            bool: 是否成功处理
        """
        # 选择通道
        channel = self.select_channel()
        if not channel:
            logging.error(f"块 {idx} 没有可用的AI通道")
            return False

        # 获取通道锁
        channel_lock = self.channel_locks[channel.name]

        start_time = time.time()
        success = False
        error_msg = None

        try:
            # 获取通道访问权限
            with channel_lock:
                logging.debug(f"块 {idx} 使用通道 {channel.name}")
                success = self._call_single_channel(chunk, idx, channel, cache_dir)

        except Exception as e:
            error_msg = str(e)
            logging.error(f"块 {idx} 通道 {channel.name} 调用失败: {e}")

        # 更新统计信息
        response_time = time.time() - start_time
        self.update_stats(channel.name, success, response_time, error_msg)

        return success

    def _call_single_channel(self, chunk: str, idx: int, channel: ChannelConfig, cache_dir: str = None) -> bool:
        """调用单个通道的API"""
        url = channel.api_base_url.rstrip("/") + "/chat/completions"
        headers = {
            "Authorization": f"Bearer {channel.api_key}",
            "Content-Type": "application/json",
        }

        payload = self._build_payload(chunk, channel)
        
        max_retries = 3
        base_delay = 2  # 增加基础延迟

        for attempt in range(max_retries):
            try:
                stream_mode = channel.use_streaming
                logging.debug(f"块 {idx} 第 {attempt + 1} 次尝试调用 AI API ({'流式' if stream_mode else '非流式'})")

                resp = requests.post(
                    url,
                    headers=headers,
                    json=payload,
                    timeout=channel.request_timeout,
                    stream=stream_mode,  # 根据通道配置决定是否流式
                )

                if resp.status_code == 200:
                    try:
                        # 根据模式收集响应内容
                        if stream_mode:
                            content = self._collect_stream_response(resp)
                        else:
                            content = self._collect_non_stream_response(resp)

                        if content:
                            # 清理响应内容
                            content = self._clean_json_response(content, idx)

                            # 解析和验证响应
                            valid_questions = self._parse_and_validate_response(content, idx)

                            # 保存到缓存
                            if cache_dir and valid_questions:
                                cache_path = Path(cache_dir) / f"{idx}.json"

                                # 如果原文件存在，先备份
                                if cache_path.exists():
                                    backup_path = cache_path.with_suffix('.json.backup')
                                    cache_path.rename(backup_path)
                                    logging.info(f"块 {idx} 原文件已备份为 {backup_path}")

                                # 保存新内容
                                with open(cache_path, "w", encoding="utf-8") as f:
                                    json.dump(valid_questions, f, ensure_ascii=False, indent=2)

                                logging.info(f"块 {idx} 成功提取 {len(valid_questions)} 个问答对")
                                return True
                            elif not cache_dir:
                                # 不需要保存缓存，直接返回成功
                                logging.debug(f"块 {idx} 成功提取 {len(valid_questions)} 个问答对（未保存缓存）")
                                return True
                            else:
                                logging.warning(f"块 {idx} 没有提取到有效的问答对")
                                return False
                        else:
                            logging.error(f"块 {idx} 响应内容为空")

                    except Exception as e:
                        logging.error(f"块 {idx} 处理响应失败: {e}")
                        
                elif resp.status_code == 429:
                    # 速率限制，使用指数退避
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 2)
                    logging.warning(f"块 {idx} 遇到速率限制 (429)，{delay:.2f}s 后重试")
                    time.sleep(delay)
                    continue
                    
                elif 500 <= resp.status_code < 600:
                    # 服务器错误，使用指数退避
                    delay = base_delay * (2 ** attempt)
                    logging.warning(f"块 {idx} 服务器错误 ({resp.status_code})，{delay}s 后重试")
                    time.sleep(delay)
                    continue
                    
                else:
                    logging.error(f"块 {idx} API 返回错误状态码 {resp.status_code}: {resp.text[:500]}...")
                    break
                    
            except Timeout:
                delay = base_delay * (2 ** attempt)
                logging.warning(f"块 {idx} 请求超时，{delay}s 后重试 (第 {attempt + 1}/{max_retries} 次)")
                if attempt < max_retries - 1:
                    time.sleep(delay)
                
            except RequestException as e:
                logging.error(f"块 {idx} 网络请求异常: {e}")
                break
                
            except Exception as e:
                logging.error(f"块 {idx} 未知异常: {e}")
                break

        logging.error(f"块 {idx} 处理失败，已尝试 {max_retries} 次")
        return False
