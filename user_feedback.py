#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
user_feedback.py

用户反馈和错误处理工具
"""

import sys
from pathlib import Path
from typing import List, Dict, Optional
from file_manager import FileManager


class UserFeedback:
    """用户反馈和错误处理工具"""
    
    def __init__(self, file_manager: FileManager = None):
        """
        初始化用户反馈工具
        
        Args:
            file_manager: 文件管理器实例
        """
        self.file_manager = file_manager or FileManager()
    
    def show_project_not_found_help(self, partial_name: str):
        """
        显示项目未找到的帮助信息
        
        Args:
            partial_name: 用户输入的部分项目名称
        """
        print(f"❌ 未找到匹配 '{partial_name}' 的项目")
        print()
        
        # 显示所有可用项目
        projects = self.file_manager.list_projects()
        if projects:
            print("📁 可用的项目列表:")
            for i, project in enumerate(projects[:10], 1):  # 最多显示10个
                status_parts = []
                if project['has_images']:
                    status_parts.append(f"{project['image_count']}张图片")
                if project['has_anki']:
                    status_parts.append("已生成Anki")
                
                status = f" ({', '.join(status_parts)})" if status_parts else ""
                print(f"  {i:2d}. {project['name']}{status}")
            
            if len(projects) > 10:
                print(f"     ... 还有 {len(projects) - 10} 个项目")
        else:
            print("📁 暂无可用项目")
            print("   请先使用以下命令处理文档:")
            print("   python document_processor.py your_document.pdf")
        
        print()
        print("💡 使用提示:")
        print("  - 项目名称至少需要5个字符")
        print("  - 支持前缀匹配: 'tiku_2' 可以匹配 'tiku_20250615_003402'")
        print("  - 支持包含匹配: '数学' 可以匹配包含'数学'的项目名")
        print("  - 使用 --list 查看所有项目详情")
    
    def show_markdown_not_found_help(self, project_name: str):
        """
        显示markdown文件未找到的帮助信息
        
        Args:
            project_name: 项目名称
        """
        print(f"❌ 项目 '{project_name}' 中没有找到markdown文件")
        print()
        
        # 检查项目目录
        extraction_dir = self.file_manager.subdirs['extracted'] / project_name
        if extraction_dir.exists():
            files = list(extraction_dir.iterdir())
            if files:
                print(f"📂 项目目录内容 ({extraction_dir}):")
                for file_path in files:
                    if file_path.is_file():
                        size_kb = file_path.stat().st_size / 1024
                        print(f"  📄 {file_path.name} ({size_kb:.1f}KB)")
                    elif file_path.is_dir():
                        file_count = len(list(file_path.iterdir()))
                        print(f"  📁 {file_path.name}/ ({file_count} 个文件)")
            else:
                print(f"📂 项目目录为空: {extraction_dir}")
        else:
            print(f"📂 项目目录不存在: {extraction_dir}")
        
        print()
        print("💡 可能的解决方案:")
        print("  1. 重新处理原始文档:")
        print("     python document_processor.py original_document.pdf")
        print("  2. 检查文档处理是否完成")
        print("  3. 使用 --md-to-anki 直接处理markdown文件:")
        print("     python document_processor.py --md-to-anki your_file.md")
    
    def show_ai_service_not_configured_help(self):
        """显示AI服务未配置的帮助信息"""
        print("❌ AI服务未初始化，无法生成Anki闪卡")
        print()
        print("🔧 配置步骤:")
        print("  1. 检查 config.ini 文件是否存在")
        print("  2. 确保以下配置项已设置:")
        print("     API_BASE_URL = your_api_url")
        print("     API_KEY = your_api_key")
        print("     MODEL_NAME = your_model_name")
        print("     API_PROVIDER = openai  # 或其他提供商")
        print()
        print("  3. 或者设置环境变量:")
        print("     set API_BASE_URL=your_api_url")
        print("     set API_KEY=your_api_key")
        print("     set MODEL_NAME=your_model_name")
        print("     set API_PROVIDER=openai")
    
    def show_file_processing_help(self, file_path: str, error: str):
        """
        显示文件处理错误的帮助信息
        
        Args:
            file_path: 文件路径
            error: 错误信息
        """
        print(f"❌ 处理文件失败: {file_path}")
        print(f"   错误: {error}")
        print()
        
        path = Path(file_path)
        
        if not path.exists():
            print("💡 文件不存在，请检查:")
            print(f"  - 文件路径是否正确: {file_path}")
            print("  - 文件是否被移动或删除")
            print("  - 路径中是否包含特殊字符")
        elif not path.is_file():
            print("💡 不是有效文件，请检查:")
            print(f"  - 是否指向了目录而不是文件")
            print(f"  - 使用 --dir 参数处理目录")
        else:
            ext = path.suffix.lower()
            supported_exts = ['.pdf', '.docx', '.doc', '.md']
            
            if ext not in supported_exts:
                print(f"💡 不支持的文件类型: {ext}")
                print(f"  支持的文件类型: {', '.join(supported_exts)}")
            else:
                print("💡 可能的解决方案:")
                print("  - 检查文件是否损坏")
                print("  - 确保文件没有被其他程序占用")
                print("  - 尝试重新下载或获取文件")
    
    def show_success_summary(self, result: Dict):
        """
        显示成功处理的摘要信息
        
        Args:
            result: 处理结果字典
        """
        print("🎉 处理完成!")
        print("=" * 50)
        
        if 'project_name' in result:
            print(f"📁 项目名称: {result['project_name']}")
        
        if 'markdown_file' in result:
            print(f"📄 Markdown文件: {result['markdown_file']}")
        
        if 'output_file' in result:
            print(f"📋 Anki文件: {result['output_file']}")
        
        if 'total_cards' in result:
            print(f"🃏 总卡片数: {result['total_cards']}")
        
        if 'questions_with_images' in result:
            print(f"🖼️  包含图片的卡片: {result['questions_with_images']}")
        
        if 'total_images' in result:
            print(f"📸 图片总数: {result['total_images']}")
        
        if 'total_pages' in result:
            print(f"📖 总页数: {result['total_pages']}")
        
        if 'image_count' in result:
            print(f"🖼️  提取图片数: {result['image_count']}")
        
        print("=" * 50)
        
        # 提供后续操作建议
        if 'output_file' in result:
            print("📝 后续操作:")
            print(f"  - 将 {Path(result['output_file']).name} 导入到Anki中")
            print("  - 检查生成的闪卡内容")
            print("  - 根据需要调整学习设置")
    
    def confirm_action(self, message: str, default: bool = True) -> bool:
        """
        确认用户操作
        
        Args:
            message: 确认消息
            default: 默认选择
            
        Returns:
            用户确认结果
        """
        default_text = "[Y/n]" if default else "[y/N]"
        try:
            response = input(f"{message} {default_text}: ").strip().lower()
            
            if not response:
                return default
            
            return response in ['y', 'yes', '是', '确定']
            
        except KeyboardInterrupt:
            print("\n❌ 操作已取消")
            return False


def main():
    """测试用户反馈功能"""
    feedback = UserFeedback()
    
    # 测试项目未找到帮助
    print("测试项目未找到帮助:")
    feedback.show_project_not_found_help("test_project")
    
    print("\n" + "="*60 + "\n")
    
    # 测试AI服务未配置帮助
    print("测试AI服务未配置帮助:")
    feedback.show_ai_service_not_configured_help()


if __name__ == "__main__":
    main()
