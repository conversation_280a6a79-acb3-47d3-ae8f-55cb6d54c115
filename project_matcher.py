#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
project_matcher.py

智能项目匹配工具，支持模糊匹配和智能文件检测
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from difflib import SequenceMatcher
import logging


class ProjectMatcher:
    """项目智能匹配器"""
    
    def __init__(self, base_dir: str = "documents"):
        """
        初始化项目匹配器
        
        Args:
            base_dir: 基础目录路径
        """
        self.base_dir = Path(base_dir)
        self.extracted_dir = self.base_dir / "extracted"
    
    def find_matching_projects(self, partial_name: str, min_length: int = 5) -> List[Dict[str, str]]:
        """
        根据部分名称查找匹配的项目
        
        Args:
            partial_name: 部分项目名称
            min_length: 最小匹配长度
            
        Returns:
            匹配的项目列表，按相似度排序
        """
        if len(partial_name) < min_length:
            raise ValueError(f"项目名称至少需要 {min_length} 个字符")
        
        if not self.extracted_dir.exists():
            return []
        
        matches = []
        partial_lower = partial_name.lower()
        
        for project_dir in self.extracted_dir.iterdir():
            if not project_dir.is_dir():
                continue
                
            project_name = project_dir.name
            project_lower = project_name.lower()
            
            # 计算匹配度
            match_score = self._calculate_match_score(partial_lower, project_lower)
            
            if match_score > 0:
                # 查找markdown文件
                md_files = self._find_markdown_files(project_dir)
                
                match_info = {
                    'name': project_name,
                    'path': str(project_dir),
                    'score': match_score,
                    'markdown_files': md_files,
                    'has_markdown': len(md_files) > 0
                }
                matches.append(match_info)
        
        # 按匹配度排序（降序）
        matches.sort(key=lambda x: x['score'], reverse=True)
        return matches
    
    def _calculate_match_score(self, partial: str, full: str) -> float:
        """
        计算匹配分数
        
        Args:
            partial: 部分字符串
            full: 完整字符串
            
        Returns:
            匹配分数 (0-1)
        """
        # 前缀匹配得分最高
        if full.startswith(partial):
            return 0.9 + (len(partial) / len(full)) * 0.1
        
        # 包含匹配
        if partial in full:
            return 0.7 + (len(partial) / len(full)) * 0.2
        
        # 序列相似度匹配
        similarity = SequenceMatcher(None, partial, full).ratio()
        if similarity >= 0.6:
            return similarity * 0.6
        
        return 0.0
    
    def _find_markdown_files(self, project_dir: Path) -> List[str]:
        """
        在项目目录中查找markdown文件
        
        Args:
            project_dir: 项目目录路径
            
        Returns:
            markdown文件路径列表
        """
        md_files = []
        
        # 查找所有.md文件
        for md_file in project_dir.glob('*.md'):
            md_files.append(str(md_file))
        
        return sorted(md_files)
    
    def get_best_match(self, partial_name: str) -> Optional[Dict[str, str]]:
        """
        获取最佳匹配项目
        
        Args:
            partial_name: 部分项目名称
            
        Returns:
            最佳匹配项目信息，如果没有找到则返回None
        """
        matches = self.find_matching_projects(partial_name)
        
        if not matches:
            return None
        
        # 返回得分最高且有markdown文件的项目
        for match in matches:
            if match['has_markdown']:
                return match
        
        # 如果没有带markdown文件的项目，返回得分最高的
        return matches[0] if matches else None
    
    def select_markdown_file(self, project_info: Dict[str, str]) -> Optional[str]:
        """
        智能选择markdown文件
        
        Args:
            project_info: 项目信息
            
        Returns:
            选择的markdown文件路径，如果没有则返回None
        """
        md_files = project_info['markdown_files']
        
        if not md_files:
            return None
        
        if len(md_files) == 1:
            return md_files[0]
        
        # 多个文件时，优先选择与项目名称最相似的
        project_name = project_info['name']
        best_file = None
        best_score = 0
        
        for md_file in md_files:
            file_name = Path(md_file).stem
            score = SequenceMatcher(None, project_name.lower(), file_name.lower()).ratio()
            
            if score > best_score:
                best_score = score
                best_file = md_file
        
        return best_file
    
    def interactive_project_selection(self, partial_name: str) -> Optional[Tuple[str, str]]:
        """
        交互式项目选择
        
        Args:
            partial_name: 部分项目名称
            
        Returns:
            (项目名称, markdown文件路径) 或 None
        """
        matches = self.find_matching_projects(partial_name)
        
        if not matches:
            print(f"❌ 没有找到匹配 '{partial_name}' 的项目")
            return None
        
        # 过滤出有markdown文件的项目
        valid_matches = [m for m in matches if m['has_markdown']]
        
        if not valid_matches:
            print(f"❌ 找到 {len(matches)} 个匹配项目，但都没有markdown文件")
            for match in matches[:3]:  # 显示前3个
                print(f"  - {match['name']}")
            return None
        
        if len(valid_matches) == 1:
            # 只有一个匹配项目
            project = valid_matches[0]
            md_file = self.select_markdown_file(project)
            print(f"✅ 自动选择项目: {project['name']}")
            print(f"📄 使用markdown文件: {Path(md_file).name}")
            return project['name'], md_file
        
        # 多个匹配项目，显示选择菜单
        print(f"🔍 找到 {len(valid_matches)} 个匹配项目:")
        for i, match in enumerate(valid_matches[:5], 1):  # 最多显示5个
            md_count = len(match['markdown_files'])
            print(f"  {i}. {match['name']} ({md_count} 个markdown文件)")
        
        try:
            choice = input("\n请选择项目编号 (1-{}): ".format(min(5, len(valid_matches))))
            index = int(choice) - 1
            
            if 0 <= index < len(valid_matches):
                project = valid_matches[index]
                md_file = self.select_markdown_file(project)
                
                if len(project['markdown_files']) > 1:
                    print(f"📄 自动选择markdown文件: {Path(md_file).name}")
                
                return project['name'], md_file
            else:
                print("❌ 无效的选择")
                return None
                
        except (ValueError, KeyboardInterrupt):
            print("❌ 操作已取消")
            return None


def main():
    """测试项目匹配器功能"""
    matcher = ProjectMatcher()
    
    # 测试模糊匹配
    test_names = ["tiku", "tiku_2", "冲刺版"]
    
    for name in test_names:
        print(f"\n测试匹配: '{name}'")
        matches = matcher.find_matching_projects(name)
        
        if matches:
            print(f"找到 {len(matches)} 个匹配项目:")
            for match in matches[:3]:  # 显示前3个
                print(f"  - {match['name']} (得分: {match['score']:.2f}, markdown文件: {len(match['markdown_files'])})")
        else:
            print("没有找到匹配项目")


if __name__ == "__main__":
    main()
