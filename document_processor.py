#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
document_processor.py

统一的文档处理器，支持多种文档格式的提取和转换
整合PDF、Word等文档类型的处理，提供统一的接口
"""

import sys
import logging
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple

from file_manager import FileManager
from pdf_extractor import PDFExtractor
from word_extractor import WordExtractor
from ai_service import AIService
from multi_channel_config import MultiChannelConfigParser
from anki_generator import AnkiGenerator
from md_to_anki import make_chunks, read_input_lines


class DocumentProcessor:
    """统一文档处理器"""
    
    def __init__(self, base_dir: str = "documents", config_file: str = "config.ini"):
        """
        初始化文档处理器

        Args:
            base_dir: 基础工作目录
            config_file: 配置文件路径
        """
        self.file_manager = FileManager(base_dir)

        # 使用新的多通道配置解析器
        self.config_parser = MultiChannelConfigParser(config_file)
        self.config = self.config_parser.get_config()
        
        # 初始化各种提取器
        self.pdf_extractor = PDFExtractor(self.file_manager)
        try:
            self.word_extractor = WordExtractor(self.file_manager)
            self.word_available = True
        except ImportError:
            self.word_extractor = None
            self.word_available = False
            logging.warning("Word提取器不可用，请安装 python-docx")
        
        # 支持的文档类型
        self.supported_extensions = {
            '.pdf': 'pdf',
            '.docx': 'word',
            '.doc': 'word',
            '.md': 'markdown',
        }

        # 初始化AI服务和Anki生成器（如果配置了AI相关参数）
        self.ai_service = None
        self.anki_generator = None
        if self._has_ai_config():
            try:
                # 使用重构后的AI服务（原生支持多通道）
                self.ai_service = AIService(self.config)
                self.anki_generator = AnkiGenerator()
                logging.info("AI服务和Anki生成器初始化成功")

                # 显示通道信息
                stats = self.ai_service.get_stats()
                logging.info(f"已启用 {stats['active_channels']} 个AI通道")

            except Exception as e:
                logging.error(f"AI服务初始化失败: {e}")
                self.ai_service = None
                self.anki_generator = None
    


    def _has_ai_config(self) -> bool:
        """检查是否配置了AI相关参数"""
        return self.config_parser.has_ai_channels()

    def _copy_images_for_anki(self, project_name: str) -> bool:
        """
        将项目的图片从extracted文件夹复制到images文件夹，并按项目名称组织

        Args:
            project_name: 项目名称

        Returns:
            bool: 是否成功复制图片
        """
        import shutil

        # 修复：正确获取项目路径 - 项目直接在 extracted 目录下
        extracted_dir = self.file_manager.base_dir / "extracted" / project_name

        if not extracted_dir.exists():
            logging.info(f"项目 {project_name} 没有提取的文件夹，跳过图片复制")
            return True

        # 查找项目中的图片文件夹（可能是 images 或重命名后的文件夹）
        image_folders = []
        for item in extracted_dir.iterdir():
            if item.is_dir() and ('images' in item.name.lower()):
                image_folders.append(item)

        if not image_folders:
            logging.info(f"项目 {project_name} 没有图片文件夹，跳过图片复制")
            return True

        # 创建images目录结构
        images_base_dir = Path("images")
        images_project_dir = images_base_dir / project_name

        try:
            images_project_dir.mkdir(parents=True, exist_ok=True)

            # 复制所有图片文件
            image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg'}
            copied_count = 0

            for image_folder in image_folders:
                for img_file in image_folder.iterdir():
                    if img_file.is_file() and img_file.suffix.lower() in image_extensions:
                        dest_file = images_project_dir / img_file.name
                        shutil.copy2(img_file, dest_file)
                        copied_count += 1

            logging.info(f"成功复制 {copied_count} 个图片文件到 {images_project_dir}")
            return True

        except Exception as e:
            logging.error(f"复制图片文件失败: {e}")
            return False

    def _update_image_paths_in_markdown(self, markdown_content: str, project_name: str) -> str:
        """
        更新Markdown内容中的图片路径，从extracted路径改为Anki格式路径

        Args:
            markdown_content: 原始Markdown内容
            project_name: 项目名称

        Returns:
            str: 更新后的Markdown内容
        """
        # 使用新的工具模块
        from document_utils import MarkdownImagePathUpdater

        path_updater = MarkdownImagePathUpdater()
        return path_updater.update_image_paths_for_anki(markdown_content, project_name, "images")
    
    def process_document(self, file_path: str) -> Dict[str, Union[str, int]]:
        """
        处理单个文档
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            处理结果字典
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文档文件不存在: {file_path}")
        
        # 检查文件类型
        file_ext = file_path.suffix.lower()
        if file_ext not in self.supported_extensions:
            raise ValueError(f"不支持的文档类型: {file_ext}")
        
        doc_type = self.supported_extensions[file_ext]
        
        logging.info(f"开始处理 {doc_type.upper()} 文档: {file_path}")
        
        # 根据文档类型调用相应的提取器
        if doc_type == 'pdf':
            result = self.pdf_extractor.extract_from_pdf(
                str(file_path),
                self.config['pdf_top_margin'],
                self.config['pdf_bottom_margin']
            )
        elif doc_type == 'word':
            if not self.word_available:
                raise RuntimeError("Word提取器不可用，请安装 python-docx")
            result = self.word_extractor.extract_from_word(str(file_path))
        else:
            raise ValueError(f"未实现的文档类型处理器: {doc_type}")
        
        # 添加通用信息
        result['document_type'] = doc_type
        result['original_file'] = str(file_path)
        result['file_size'] = file_path.stat().st_size
        
        logging.info(f"{doc_type.upper()} 文档处理完成: {result['project_name']}")
        
        return result
    
    def process_multiple_documents(self, file_paths: List[str]) -> List[Dict[str, Union[str, int]]]:
        """
        批量处理多个文档
        
        Args:
            file_paths: 文档文件路径列表
            
        Returns:
            处理结果列表
        """
        results = []
        
        logging.info(f"开始批量处理 {len(file_paths)} 个文档")
        
        for i, file_path in enumerate(file_paths, 1):
            try:
                logging.info(f"[{i}/{len(file_paths)}] 处理文档: {file_path}")
                result = self.process_document(file_path)
                results.append(result)
                
            except Exception as e:
                logging.error(f"处理文档 {file_path} 失败: {e}")
                # 添加失败记录
                results.append({
                    'original_file': file_path,
                    'error': str(e),
                    'success': False
                })
        
        # 统计处理结果
        success_count = sum(1 for r in results if r.get('success', True) and 'error' not in r)
        failed_count = len(results) - success_count
        
        logging.info(f"批量处理完成: 成功 {success_count}, 失败 {failed_count}")
        
        return results
    
    def process_directory(self, directory: str, recursive: bool = True) -> List[Dict[str, Union[str, int]]]:
        """
        处理目录中的所有支持的文档
        
        Args:
            directory: 目录路径
            recursive: 是否递归处理子目录
            
        Returns:
            处理结果列表
        """
        dir_path = Path(directory)
        
        if not dir_path.exists() or not dir_path.is_dir():
            raise ValueError(f"目录不存在或不是有效目录: {directory}")
        
        # 查找支持的文档文件
        file_paths = []
        
        if recursive:
            for ext in self.supported_extensions.keys():
                file_paths.extend(dir_path.rglob(f"*{ext}"))
        else:
            for ext in self.supported_extensions.keys():
                file_paths.extend(dir_path.glob(f"*{ext}"))
        
        # 转换为字符串路径
        file_paths = [str(fp) for fp in file_paths]
        
        logging.info(f"在目录 {directory} 中找到 {len(file_paths)} 个支持的文档")
        
        if not file_paths:
            logging.warning("未找到任何支持的文档文件")
            return []
        
        return self.process_multiple_documents(file_paths)
    
    def get_processing_summary(self) -> Dict[str, Union[int, List]]:
        """
        获取处理摘要信息
        
        Returns:
            处理摘要字典
        """
        projects = self.file_manager.list_projects()
        
        summary = {
            'total_projects': len(projects),
            'projects_with_images': sum(1 for p in projects if p['has_images']),
            'projects_with_cache': sum(1 for p in projects if p['has_cache']),
            'projects_with_anki': sum(1 for p in projects if p['has_anki']),
            'total_images': sum(p['image_count'] for p in projects),
            'projects': projects
        }
        
        return summary
    
    def generate_anki_for_project(self, project_name: str, interactive: bool = True) -> Dict[str, Union[str, int]]:
        """
        为指定项目生成Anki闪卡，支持模糊匹配

        Args:
            project_name: 项目名称（支持部分匹配）
            interactive: 是否启用交互式选择

        Returns:
            生成结果字典
        """
        if not self.ai_service or not self.anki_generator:
            raise RuntimeError("AI服务未初始化，请检查配置文件中的API相关设置")

        # 使用智能项目匹配
        from project_matcher import ProjectMatcher
        from markdown_detector import MarkdownDetector

        matcher = ProjectMatcher(str(self.file_manager.base_dir))
        md_detector = MarkdownDetector()

        # 查找匹配的项目
        if interactive:
            match_result = matcher.interactive_project_selection(project_name)
            if not match_result:
                raise ValueError(f"未找到匹配的项目: {project_name}")

            actual_project_name, markdown_file = match_result
        else:
            # 非交互模式，使用最佳匹配
            best_match = matcher.get_best_match(project_name)
            if not best_match:
                raise ValueError(f"未找到匹配的项目: {project_name}")

            actual_project_name = best_match['name']
            markdown_file = md_detector.select_best_markdown(best_match['path'], actual_project_name)

            if not markdown_file:
                raise FileNotFoundError(f"项目 {actual_project_name} 中没有找到markdown文件")

        # 验证markdown文件
        is_valid, error_msg = md_detector.validate_markdown_file(markdown_file)
        if not is_valid:
            raise FileNotFoundError(f"Markdown文件无效: {error_msg}")

        logging.info(f"开始为项目 {actual_project_name} 生成Anki闪卡")
        logging.info(f"使用markdown文件: {markdown_file}")

        # 注意：不再复制图片到images文件夹，图片已在项目文件夹内组织良好
        logging.info("跳过图片复制步骤 - 图片已在项目文件夹内正确组织")

        # 读取并处理Markdown内容
        try:
            with open(markdown_file, 'r', encoding='utf-8') as f:
                markdown_content = f.read()

            # 图片路径已经在项目文件夹内正确组织，无需更新

            # 获取项目配置
            project_config = self.file_manager.get_config_for_project(actual_project_name)
            project_config['INPUT_FILE'] = markdown_file  # 使用实际找到的markdown文件

            # 处理Markdown内容生成Anki
            result = self._process_markdown_to_anki(
                markdown_content,
                actual_project_name,
                project_config
            )

            return result

        except Exception as e:
            logging.error(f"生成Anki闪卡失败: {e}")
            raise

    def _process_markdown_to_anki(self, markdown_content: str, project_name: str, project_config: Dict) -> Dict[str, Union[str, int]]:
        """
        处理Markdown内容生成Anki闪卡

        Args:
            markdown_content: Markdown内容
            project_name: 项目名称
            project_config: 项目配置

        Returns:
            处理结果字典
        """
        from concurrent.futures import ThreadPoolExecutor, as_completed
        from tqdm import tqdm

        # 将内容写入临时文件，然后使用现有的读取函数
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(markdown_content)
            temp_file_path = temp_file.name

        try:
            # 使用现有的读取函数
            lines = read_input_lines(temp_file_path, self.config.get('lines_to_skip', 13))
        finally:
            # 清理临时文件
            Path(temp_file_path).unlink(missing_ok=True)

        if not lines:
            raise ValueError("处理后的Markdown内容为空")

        logging.info(f"准备处理 {len(lines)} 行Markdown内容")

        # 创建文本块
        chunks = make_chunks(lines, self.config['chunk_size'], self.config['chunk_stride'])

        if not chunks:
            raise ValueError("没有生成任何文本块")

        # 设置缓存目录
        cache_dir = Path(project_config['CACHE_DIR'])
        cache_dir.mkdir(exist_ok=True)

        # 并发处理文本块
        logging.info(f"开始处理 {len(chunks)} 个文本块...")

        with ThreadPoolExecutor(max_workers=self.config['max_workers']) as executor:
            # 提交需要处理的任务（跳过已缓存的）
            futures = {}
            for idx, text in chunks:
                cache_file = cache_dir / f"{idx}.json"
                if not cache_file.exists():
                    future = executor.submit(self.ai_service.call_api, text, idx, str(cache_dir))
                    futures[future] = idx

            # 显示进度并等待结果
            success_count = 0
            if futures:
                logging.info(f"需要处理 {len(futures)} 个新块，{len(chunks) - len(futures)} 个块已缓存")

                for fut in tqdm(as_completed(futures), total=len(futures), desc="AI 处理进度"):
                    idx = futures[fut]
                    try:
                        success = fut.result()
                        if success:
                            success_count += 1
                    except Exception as e:
                        logging.error(f"块 {idx} 处理异常: {e}")

                logging.info(f"AI 处理完成: {success_count}/{len(futures)} 个块成功处理")
            else:
                logging.info("所有块都已缓存，跳过 AI 处理")

        # 生成Anki文件
        output_file = project_config['OUTPUT_FILE']
        self.anki_generator.cache_dir = cache_dir
        stats = self.anki_generator.generate_anki_file(output_file, len(chunks))

        if stats["total_cards"] == 0:
            raise ValueError("没有提取到任何问答对，请检查输入内容格式")

        logging.info(f"成功生成 {stats['total_cards']} 张Anki卡片")

        return {
            'project_name': project_name,
            'input_file': project_config['INPUT_FILE'],
            'output_file': output_file,
            'cache_dir': str(cache_dir),
            'total_cards': stats['total_cards'],
            'questions_with_images': stats['questions_with_images'],
            'total_images': stats['total_images'],
            'source_chunks': stats['source_chunks'],
            'status': 'completed'
        }



    def process_markdown_to_anki(self, markdown_file: str, output_file: str = None,
                                cache_dir: str = None, project_name: str = None) -> Dict[str, Union[str, int]]:
        """
        直接处理Markdown文件生成Anki闪卡

        Args:
            markdown_file: Markdown文件路径
            output_file: 输出Anki文件路径，默认为 markdown文件名.txt
            cache_dir: 缓存目录，默认为 cache
            project_name: 项目名称（用于图片管理）

        Returns:
            处理结果字典
        """
        if not self.ai_service or not self.anki_generator:
            raise RuntimeError("AI服务未初始化，请检查配置文件中的API相关设置")

        markdown_path = Path(markdown_file)
        if not markdown_path.exists():
            raise FileNotFoundError(f"Markdown文件不存在: {markdown_file}")

        # 设置默认参数
        if output_file is None:
            output_file = markdown_path.with_suffix('.txt').name

        if cache_dir is None:
            cache_dir = "cache"

        if project_name is None:
            project_name = markdown_path.stem

        logging.info(f"开始处理Markdown文件: {markdown_file}")

        # 注意：不再复制图片，图片已在项目文件夹内正确组织
        logging.info("跳过图片复制步骤 - 图片已在项目文件夹内正确组织")

        # 读取并处理Markdown内容
        try:
            with open(markdown_file, 'r', encoding='utf-8') as f:
                markdown_content = f.read()

            # 图片路径已经在项目文件夹内正确组织，无需更新

            # 创建临时项目配置
            temp_config = {
                'INPUT_FILE': str(markdown_path),
                'OUTPUT_FILE': output_file,
                'CACHE_DIR': cache_dir
            }

            # 处理Markdown内容生成Anki
            result = self._process_markdown_to_anki(
                markdown_content,
                project_name,
                temp_config
            )

            return result

        except Exception as e:
            logging.error(f"处理Markdown文件失败: {e}")
            raise


def main():
    """命令行接口"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python document_processor.py <file_path>                    # 处理单个文档为Markdown")
        print("  python document_processor.py --dir <directory>              # 处理目录中的所有文档为Markdown")
        print("  python document_processor.py --list                         # 列出所有项目")
        print("  python document_processor.py --summary                      # 显示处理摘要")
        print("  python document_processor.py --anki <partial_name>          # 智能匹配项目生成Anki闪卡")
        print("  python document_processor.py --anki <partial_name> --no-interactive  # 非交互模式")
        print("  python document_processor.py --md-to-anki <md_file>         # 直接将任意Markdown文件转换为Anki")
        print("  python document_processor.py --full <file_path>             # 完整流程：文档→MD→Anki")
        print("  python document_processor.py --channels                     # 显示AI通道状态")
        print("")
        print("--anki 命令支持模糊匹配:")
        print("  示例: --anki tiku_2  可以匹配 tiku_20250615_003402")
        print("  最少需要5个字符，支持前缀匹配和包含匹配")
        sys.exit(1)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s'
    )
    
    processor = DocumentProcessor()
    
    try:
        if sys.argv[1] == '--dir':
            # 处理目录
            if len(sys.argv) < 3:
                print("请指定目录路径")
                sys.exit(1)
            
            directory = sys.argv[2]
            results = processor.process_directory(directory)
            
            print(f"\n目录处理完成，共处理 {len(results)} 个文档:")
            for result in results:
                if 'error' in result:
                    print(f"  ❌ {result['original_file']}: {result['error']}")
                else:
                    print(f"  ✅ {result['project_name']}: {result['document_type'].upper()}")
        
        elif sys.argv[1] == '--list':
            # 列出项目
            projects = processor.file_manager.list_projects()
            print(f"\n共有 {len(projects)} 个项目:")
            for project in projects:
                status = []
                if project['has_images']:
                    status.append(f"{project['image_count']}张图片")
                if project['has_cache']:
                    status.append("已缓存")
                if project['has_anki']:
                    status.append("已生成Anki")
                
                status_str = f" ({', '.join(status)})" if status else ""
                print(f"  - {project['name']}{status_str}")
        
        elif sys.argv[1] == '--summary':
            # 显示摘要
            summary = processor.get_processing_summary()
            print("\n处理摘要:")
            print(f"  总项目数: {summary['total_projects']}")
            print(f"  包含图片的项目: {summary['projects_with_images']}")
            print(f"  已缓存的项目: {summary['projects_with_cache']}")
            print(f"  已生成Anki的项目: {summary['projects_with_anki']}")
            print(f"  总图片数: {summary['total_images']}")

        elif sys.argv[1] == '--channels':
            # 显示AI通道状态
            if processor.ai_service and hasattr(processor.ai_service, 'get_stats'):
                stats = processor.ai_service.get_stats()
                print("\nAI通道状态:")
                print(f"  总通道数: {stats['total_channels']}")
                print(f"  活跃通道数: {stats['active_channels']}")
                print("\n通道详情:")

                for name, channel_stats in stats['channels'].items():
                    status_icon = "✅" if channel_stats['status'] == 'active' else "❌"
                    print(f"  {status_icon} {name}:")
                    print(f"    状态: {channel_stats['status']}")
                    print(f"    总请求数: {channel_stats['total_requests']}")
                    print(f"    成功率: {channel_stats['success_rate']}%")
                    print(f"    平均响应时间: {channel_stats['avg_response_time']}s")
                    if channel_stats['error_message']:
                        print(f"    错误信息: {channel_stats['error_message']}")
                    print()
            else:
                print("\n❌ AI服务未初始化或不支持多通道功能")

        elif sys.argv[1] == '--channels':
            # 显示AI通道状态
            if processor.ai_service and hasattr(processor.ai_service, 'get_stats'):
                stats = processor.ai_service.get_stats()
                print("\nAI通道状态:")
                print(f"  总通道数: {stats['total_channels']}")
                print(f"  活跃通道数: {stats['active_channels']}")
                print("\n通道详情:")

                for name, channel_stats in stats['channels'].items():
                    status_icon = "✅" if channel_stats['status'] == 'active' else "❌"
                    print(f"  {status_icon} {name}:")
                    print(f"    状态: {channel_stats['status']}")
                    print(f"    总请求数: {channel_stats['total_requests']}")
                    print(f"    成功率: {channel_stats['success_rate']}%")
                    print(f"    平均响应时间: {channel_stats['avg_response_time']}s")
                    if channel_stats['error_message']:
                        print(f"    错误信息: {channel_stats['error_message']}")
                    print()
            else:
                print("\n❌ AI服务未初始化或不支持多通道功能")

        elif sys.argv[1] == '--anki':
            # 为项目生成Anki闪卡（支持模糊匹配）
            if len(sys.argv) < 3:
                print("请指定项目名称（支持部分匹配，至少5个字符）")
                print("示例: python document_processor.py --anki tiku_2")
                sys.exit(1)

            project_name = sys.argv[2]

            # 检查是否有--no-interactive参数
            interactive = '--no-interactive' not in sys.argv

            try:
                print(f"🔍 搜索匹配项目: '{project_name}'...")
                result = processor.generate_anki_for_project(project_name, interactive=interactive)

                # 使用用户反馈系统显示成功信息
                from user_feedback import UserFeedback
                feedback = UserFeedback(processor.file_manager)
                feedback.show_success_summary(result)

            except ValueError as e:
                from user_feedback import UserFeedback
                feedback = UserFeedback(processor.file_manager)

                if "未找到匹配的项目" in str(e):
                    feedback.show_project_not_found_help(project_name)
                else:
                    print(f"❌ 项目匹配失败: {e}")
                sys.exit(1)

            except FileNotFoundError as e:
                from user_feedback import UserFeedback
                feedback = UserFeedback(processor.file_manager)

                if "markdown文件" in str(e).lower():
                    # 尝试从错误信息中提取项目名称
                    try:
                        from project_matcher import ProjectMatcher
                        matcher = ProjectMatcher(str(processor.file_manager.base_dir))
                        best_match = matcher.get_best_match(project_name)
                        if best_match:
                            feedback.show_markdown_not_found_help(best_match['name'])
                        else:
                            feedback.show_project_not_found_help(project_name)
                    except:
                        print(f"❌ 文件不存在: {e}")
                else:
                    print(f"❌ 文件不存在: {e}")
                sys.exit(1)

            except RuntimeError as e:
                if "AI服务未初始化" in str(e):
                    from user_feedback import UserFeedback
                    feedback = UserFeedback(processor.file_manager)
                    feedback.show_ai_service_not_configured_help()
                else:
                    print(f"❌ 运行时错误: {e}")
                sys.exit(1)

            except Exception as e:
                print(f"❌ 生成Anki闪卡失败: {e}")
                logging.error(f"生成Anki闪卡失败: {e}", exc_info=True)
                sys.exit(1)

        elif sys.argv[1] == '--md-to-anki':
            # 直接将Markdown转换为Anki
            if len(sys.argv) < 3:
                print("请指定Markdown文件路径")
                sys.exit(1)

            md_file = sys.argv[2].strip().strip('"').strip("'")
            try:
                result = processor.process_markdown_to_anki(md_file)
                print(f"\n✅ Markdown转Anki完成!")
                print(f"输入文件: {result['input_file']}")
                print(f"输出文件: {result['output_file']}")
                print(f"总卡片数: {result['total_cards']}")
                print(f"包含图片的卡片: {result['questions_with_images']}")
                print(f"图片总数: {result['total_images']}")
            except Exception as e:
                print(f"❌ Markdown转Anki失败: {e}")
                sys.exit(1)

        elif sys.argv[1] == '--full':
            # 完整流程：文档→MD→Anki
            if len(sys.argv) < 3:
                print("请指定文档文件路径")
                sys.exit(1)

            file_path = sys.argv[2].strip().strip('"').strip("'")
            try:
                # 第一步：处理文档为Markdown
                print("🔄 第一步：处理文档为Markdown...")
                doc_result = processor.process_document(file_path)
                print(f"✅ 文档处理完成: {doc_result['project_name']}")

                # 第二步：生成Anki闪卡
                print("🔄 第二步：生成Anki闪卡...")
                anki_result = processor.generate_anki_for_project(doc_result['project_name'])

                print(f"\n🎉 完整流程处理完成!")
                print(f"项目名称: {anki_result['project_name']}")
                print(f"Markdown文件: {doc_result['markdown_file']}")
                print(f"Anki文件: {anki_result['output_file']}")
                print(f"总卡片数: {anki_result['total_cards']}")
                print(f"包含图片的卡片: {anki_result['questions_with_images']}")
                print(f"图片总数: {anki_result['total_images']}")

            except Exception as e:
                print(f"❌ 完整流程处理失败: {e}")
                sys.exit(1)

        else:
            # 处理单个文档
            file_path = sys.argv[1]
            # 规范化路径，处理路径中的引号和特殊字符
            file_path = file_path.strip().strip('"').strip("'")
            result = processor.process_document(file_path)
            
            print("\n文档处理完成!")
            print(f"项目名称: {result['project_name']}")
            print(f"文档类型: {result['document_type'].upper()}")
            print(f"原始文件: {result['original_file']}")
            print(f"Markdown文件: {result['markdown_file']}")
            if 'image_count' in result:
                print(f"图片数量: {result['image_count']}")
            if 'total_pages' in result:
                print(f"总页数: {result['total_pages']}")
            if 'paragraph_count' in result:
                print(f"段落数: {result['paragraph_count']}")
    
    except Exception as e:
        print(f"处理失败: {e}")
        logging.error(f"处理失败: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
