#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_md_to_anki_mock.py

使用模拟（Mock）来测试 AI API 调用和缓存逻辑，无需真实网络请求。
"""

import json
import unittest
from unittest.mock import patch, MagicMock
from pathlib import Path
import shutil

# 必须在导入 md_to_anki 之前设置，以确保其使用测试配置
from md_to_anki import load_config, call_ai_api

class TestApiCall(unittest.TestCase):

    def setUp(self):
        """在每个测试前运行，设置环境"""
        self.cfg = load_config()
        self.cache_dir = Path(self.cfg["cache_dir"])
        # 清理旧的缓存以保证测试独立性
        if self.cache_dir.exists():
            shutil.rmtree(self.cache_dir)
        self.cache_dir.mkdir()

    def tearDown(self):
        """在每个测试后运行，清理环境"""
        if self.cache_dir.exists():
            shutil.rmtree(self.cache_dir)

    @patch('md_to_anki.requests.post')
    def test_successful_api_call_and_caching(self, mock_post):
        """
        测试：当 API 成功返回 200 和有效 JSON 时，
        脚本应能正确解析并创建缓存文件。
        """
        # 1. 准备模拟数据
        sample_chunk = "这是一个测试问题？\n答案是肯定的。"
        expected_questions = [{
            "front": "这是一个测试问题？",
            "back": "答案是肯定的。"
        }]
        mock_response = MagicMock()
        mock_response.status_code = 200
        # 模拟 AI 返回的 JSON 字符串
        ai_json_string = json.dumps({"questions": expected_questions})
        # 模拟 chat/completions 的完整响应结构
        mock_response.json.return_value = {
            "choices": [{"message": {"content": ai_json_string}}]
        }
        mock_post.return_value = mock_response

        # 2. 执行被测试的函数
        success = call_ai_api(sample_chunk, self.cfg, 0)

        # 3. 断言和验证
        self.assertTrue(success, "API 调用应该返回 True 表示成功")
        
        # 验证 requests.post 是否被正确调用
        mock_post.assert_called_once()

        # 验证缓存文件是否被正确创建和写入
        cache_file = self.cache_dir / "0.json"
        self.assertTrue(cache_file.exists(), "缓存文件应该被创建")

        with open(cache_file, "r", encoding="utf-8") as f:
            cached_data = json.load(f)
        
        self.assertEqual(cached_data, expected_questions, "缓存内容应与预期一致")
        print("\n模拟测试成功！缓存已正确创建。")

if __name__ == "__main__":
    unittest.main()