# Changelog

## v2.2 - Document Processing Logic Fixes (2025-06-15)

### 🎯 Major Bug Fixes
- **Fixed Extraction Folder Detection Logic**: Corrected critical path detection error in `_copy_images_for_anki` method that was causing "项目没有提取的文件夹，跳过图像复制" errors
- **Resolved Image Path Mismatches**: Fixed inconsistencies between Markdown image references and actual folder structure
- **Enhanced Anki Generation**: System now correctly finds and processes all extracted folders for Anki card generation

### 🆕 New Features
- **Document Name Cleaning**: Implemented comprehensive Windows-compatible file name cleaning
  - Removes/replaces problematic characters: `<>:"/\|?*`
  - Handles Chinese punctuation marks
  - Limits file name length to prevent system issues
- **Enhanced Image Folder Naming**: New naming convention `images_{document_name}` for better organization
- **Automatic Path Updates**: Markdown files automatically updated when image folders are renamed

### 📁 New Utility Modules
- **document_utils.py**: Core document processing utilities
  - `DocumentNameCleaner`: Windows file system compatibility
  - `ImageFolderManager`: Image folder naming and management
  - `MarkdownImagePathUpdater`: Automatic path updates in Markdown files
- **project_migration.py**: Migration tool for existing projects
  - Batch migration support
  - Preview mode for safe testing
  - Automatic Markdown path updates
- **fix_image_paths.py**: Standalone image path correction tool

### 🔧 System Improvements
- **Enhanced File Manager**: Updated `file_manager.py` to support new naming conventions
- **Improved Project Detection**: Better handling of renamed image folders in project listing
- **Windows Compatibility**: Enhanced support for Windows file system limitations

### 🧪 Testing Results
- **冲刺版_2024项目**: Successfully processed 97 images, generated 83 Anki cards
- **tiku_2项目**: Successfully processed 845 images, AI processing working correctly
- **Migration Success**: 3 existing projects successfully migrated to new structure

### 🔄 Migration Support
- **Backward Compatibility**: Existing projects continue to work without modification
- **Safe Migration**: Preview mode allows testing before actual changes
- **Automatic Updates**: Markdown files automatically updated during migration
- **Validation**: Enhanced validation ensures image paths remain functional

### 📝 Code Changes
#### Modified Files:
- `document_processor.py`: Fixed folder detection logic, enhanced image path handling
- `file_manager.py`: Updated folder creation and project listing logic

#### New Files:
- `document_utils.py`: Document processing utilities
- `project_migration.py`: Project migration tool
- `fix_image_paths.py`: Image path correction utility

### 🐛 Bug Fixes
- Fixed "项目没有提取的文件夹" error in Anki generation
- Resolved image folder detection issues
- Corrected Markdown image path references
- Enhanced Windows file system compatibility

### ⚡ Performance Improvements
- Faster project detection with improved folder scanning
- Batch processing for multiple project migrations
- Optimized image path updates

---

## v2.1 - Integrated Document Processing System (Previous)

### Features
- Unified multi-document processing pipeline
- Enhanced PDF and Word document support
- Advanced image positioning and filtering
- Project-based organization system
- Intelligent content filtering with spam detection

### Core Components
- Multi-document processor with Anki generation
- Unified file naming and project management
- Advanced content filtering system
- Smart image positioning for PDFs
- Comprehensive Word document support

---

## v2.0 - Multi-Document Support (Previous)

### Features
- Support for PDF and Word documents
- Project-based file organization
- Enhanced image extraction and processing
- Configurable content filtering
- Batch processing capabilities

---

## v1.x - Legacy System (Previous)

### Features
- Basic PDF to Markdown conversion
- Simple Anki card generation
- Basic image extraction
- Single document processing

## 2025-06-15 11:45:00

### 4. --anki 命令智能优化：模糊匹配与智能文件检测系统

**Change Type**: major enhancement + feature + bugfix

> **Purpose**: 优化 `document_processor.py` 的 `--anki` 命令，实现智能项目匹配、自动markdown文件检测，解决用户体验问题

**Problem Solved**:
- 用户必须输入完整准确的项目名称才能使用 `--anki` 命令
- `get_config_for_project` 方法存在 "dummy_" 前缀bug导致文件路径错误
- 缺乏智能的markdown文件检测和选择机制
- 错误信息不够友好，缺乏用户指导

**Key Features**:
1. **智能模糊匹配**: 支持部分项目名称输入（最少5字符），类似Git分支补全
2. **智能文件检测**: 自动识别和选择项目中的markdown文件
3. **交互式选择**: 多个匹配项目时提供用户友好的选择界面
4. **增强错误处理**: 智能错误诊断和详细的用户指导信息
5. **向后兼容**: 保持所有现有功能不变

**Technical Implementation**:
- 新增 `project_matcher.py`: 智能项目匹配器，支持前缀匹配、包含匹配、相似度匹配
- 新增 `markdown_detector.py`: 智能markdown文件检测器，自动文件分析和评分
- 新增 `user_feedback.py`: 用户反馈系统，提供智能错误诊断和帮助信息
- 修复 `file_manager.py` 中的 `get_config_for_project` 方法bug
- 增强 `document_processor.py` 的 `generate_anki_for_project` 方法

**Usage Examples**:
```bash
# 之前：必须输入完整项目名称
python document_processor.py --anki tiku_20250615_003402

# 现在：支持部分匹配
python document_processor.py --anki tiku_2
python document_processor.py --anki 冲刺版_2024

# 非交互模式
python document_processor.py --anki tiku_2 --no-interactive
```

**Test Results**:
- ✅ `tiku_2` 成功匹配 `tiku_20250615_003402`
- ✅ `冲刺版_2024` 成功匹配 `冲刺版_2024北sen整理题库数学百题_20250614_223822`
- ✅ 完整处理流程验证：生成82张Anki卡片
- ✅ 错误处理验证：项目未找到、名称过短、AI服务未配置等场景

**User Experience Impact**:
- 大幅简化用户操作：从需要记忆完整项目名称到只需输入部分关键字
- 智能引导：提供详细的错误信息和使用建议
- 容错性增强：支持模糊匹配，减少输入错误

```
ENHANCED FEATURES:
- project_matcher.py               // 智能项目匹配器，支持模糊匹配算法
- markdown_detector.py             // 智能markdown文件检测和选择
- user_feedback.py                 // 用户反馈系统，智能错误诊断
- file_manager.py                  // 修复get_config_for_project方法bug
- document_processor.py            // 增强--anki命令，支持模糊匹配和智能选择
```

> **Migration Path**: 完全向后兼容，现有工作流程不受影响，新功能为可选增强

---

## 2025-06-15 01:15:00

### 3. Integrated Anki Generation Pipeline - Complete Workflow Integration

**Change Type**: major integration + feature enhancement

> **Purpose**: Integrate md_to_anki functionality directly into document_processor.py, providing a unified interface for complete document processing workflows (Document → Markdown → Anki).

> **Detailed Description**:
> 1. **Unified Processing Interface**: Integrated AIService and AnkiGenerator into DocumentProcessor class for seamless workflow
> 2. **Flexible Processing Options**: Users can choose to process documents to Markdown only, generate Anki from existing projects, or run complete pipeline
> 3. **Smart Image Management**: Automatic image copying from extracted/ to images/<project_name>/ with path updates for Anki compatibility
> 4. **Enhanced Configuration System**: Extended config loading to support AI-related parameters (API_KEY, API_BASE_URL, MODEL_NAME, etc.)
> 5. **Code Reuse Optimization**: Direct import of existing functions (make_chunks, read_input_lines) from md_to_anki.py to avoid duplication
> 6. **Multiple Processing Modes**:
>    - `--anki <project_name>`: Generate Anki for existing projects
>    - `--md-to-anki <md_file>`: Direct Markdown to Anki conversion
>    - `--full <file_path>`: Complete pipeline (Document → MD → Anki)
> 7. **Anki-Compatible Image Paths**: Automatic path conversion to `<project_name>/<imagename>` format for Anki media management

> **Reason for Change**:
> - Users needed a unified interface instead of running separate scripts
> - Image management between document processing and Anki generation needed coordination
> - Workflow efficiency required integrated processing options
> - Code maintainability required elimination of duplicate implementations

> **Impact Scope**: Major enhancement to DocumentProcessor with full backward compatibility

> **API Changes**:
> - DocumentProcessor: Added generate_anki_for_project(), process_markdown_to_anki() methods
> - Enhanced configuration loading with AI parameters and environment variable support
> - New image management methods: _copy_images_for_anki(), _update_image_paths_in_markdown()
> - Extended command-line interface with new processing options

> **Configuration Changes**:
> - Added AI-related configuration parameters (api_key, api_base_url, model_name, etc.)
> - Support for environment variable overrides
> - Enhanced error handling for missing AI configuration

> **Performance Impact**:
> - Unified processing reduces overhead between separate script executions
> - Smart image copying only when needed
> - Reuse of existing tested functions improves reliability

```
ENHANCED FEATURES:
- document_processor.py            // Integrated Anki generation with flexible processing modes
- Unified workflow options         // --anki, --md-to-anki, --full processing modes
- Smart image management           // Automatic copying and path updates for Anki compatibility
- Code reuse optimization          // Direct import of existing functions to avoid duplication
- Enhanced configuration           // AI parameters with environment variable support
```

> **Migration Path**: Existing workflows remain fully functional. New integrated features provide additional convenience options.

> **Usage Examples**:
> ```bash
> # Process document to Markdown only
> python document_processor.py document.pdf
>
> # Generate Anki for existing project
> python document_processor.py --anki project_name
>
> # Direct Markdown to Anki conversion
> python document_processor.py --md-to-anki notes.md
>
> # Complete pipeline
> python document_processor.py --full document.pdf
> ```

## 2025-06-15 00:30:00

### 2. PDF图片位置智能优化与广告图片过滤系统

**Change Type**: major enhancement + feature

> **Purpose**: 实现PDF图片与文本的空间智能分析，自动判断图片与文本的上下文关系，并引入广告图片自动识别与过滤（支持相似度阈值配置与被过滤图片归档）。

> **Detailed Description**:
> 1. **图片位置智能分析**：遍历PDF每一页，提取所有图片和文本的边界框，计算图片与文本的空间关系，实现图片在Markdown中的智能插入（上方/下方/中间）。
> 2. **广告图片过滤系统**：支持配置垃圾图片模板（如微信广告），对每张图片进行像素级相似度检测（支持PIL/可选OpenCV/pHash/SSIM等），并根据阈值自动过滤。
> 3. **被过滤图片归档**：所有被判定为广告的图片自动保存到 filtered_images 目录，文件名包含相似度分数，便于人工复查和阈值调整。
> 4. **广告文本过滤**：支持配置广告文本关键字，自动剔除包含指定广告语的文本块。
> 5. **配置化阈值与日志**：filter_config.ini 支持相似度阈值、模板目录、归档目录、广告文本等参数配置，日志可选详细模式。
> 6. **分析与测试工具**：提供 test_similarity_threshold.py、analyze_filtered_images.py 等工具，辅助阈值调整与过滤效果分析。

> **Reason for Change**:
> - 用户需要自动过滤PDF中的广告图片和广告文本，提升内容纯净度
> - 需要支持灵活调整过滤阈值，兼顾误杀与漏判
> - 需要归档被过滤图片，便于人工复查和持续优化

> **Impact Scope**: PDF处理链路、内容过滤、配置管理、测试分析工具

> **API Changes**:
> - content_filter.py: 新增 get_image_similarity, save_filtered_image, filter_images 支持归档与相似度分数
> - pdf_extractor.py: 支持图片与文本空间关系分析，调用内容过滤器
> - filter_config.ini: 新增过滤参数
> - 新增 filtered_images/ 目录
> - 新增分析与测试脚本

> **Performance Impact**:
> - 过滤过程略有增加，但支持批量处理和归档，便于后续优化

```
ENHANCED FEATURES:
- content_filter.py                // 广告图片过滤与相似度检测
- pdf_extractor.py                 // 智能图片位置分析与过滤集成
- filter_config.ini                // 过滤参数配置
- filtered_images/                 // 被过滤图片归档
- test_similarity_threshold.py     // 阈值测试工具
- analyze_filtered_images.py       // 过滤效果分析工具
```

## 2025-06-14 22:36:00

### 1. Enhanced Word Document Processing with Format Preservation and Image Optimization

**Change Type**: major enhancement + bug fix

> **Purpose**: Improve Word document processing with precise image placement, enhanced naming conventions, format preservation, and elimination of duplicate images.

> **Detailed Description**: 
> 1. **Enhanced Image Processing**: Improved image extraction from Word documents with precise positioning within text flow
> 2. **Intelligent Image Naming**: Added file-specific prefixes to image names for better organization (e.g., `【冲刺版】2024北sen整理题库数学百题_page_4_img_1.png`)
> 3. **Format Preservation**: Added support for Word text formatting including colors, bold, italic in Markdown output
> 4. **Precise Image Placement**: Images now appear exactly where they exist in the original document, maintaining context
> 5. **Duplicate Elimination**: Removed redundant "其他图片" section to prevent image duplication
> 6. **HTML Tag Format**: Standardized image references using `<img alt="undefined" src="images/filename.png">` format
> 7. **RGBColor Compatibility**: Fixed RGBColor attribute access for proper color extraction from Word documents
> 8. **Enhanced Error Handling**: Improved error handling for Word document format detection and processing

> **Reason for Change**: 
> - Users needed images to appear in precise locations within text for proper context
> - Image naming needed to include source file information for mixed document processing
> - Word formatting (colors, bold) needed to be preserved in Markdown output
> - Duplicate images were causing confusion and redundancy
> - RGBColor processing was failing due to incorrect attribute access

> **Impact Scope**: Significant improvement in Word document processing quality and user experience.

> **API Changes**: 
> - Enhanced `_extract_paragraph_images()` method with precise positioning
> - Updated `_format_paragraph_runs()` for better format preservation
> - Improved `_get_image_extension()` for content type handling
> - Eliminated `_extract_document_images()` to prevent duplication

> **Configuration Changes**: 
> - Enhanced image naming with file-specific prefixes
> - Improved format preservation settings
> - Better error handling for unsupported formats (.doc vs .docx)

> **Performance Impact**: 
> - Reduced processing time by eliminating duplicate image extraction
> - Improved memory usage with single-pass image processing
> - Better user experience with precise image placement

```
ENHANCED FEATURES:
- word_extractor.py                // Enhanced with format preservation and precise image placement
- document_processor.py            // Improved error handling and path processing
- Image naming with file prefixes  // Better organization for mixed documents
- HTML-style image tags            // Standardized image reference format
- Color and bold text support      // Word formatting preservation in Markdown
```

> **Migration Path**: Existing workflows benefit immediately from improved processing quality.

> **Testing**: Successfully processed complex Word documents with mixed formatting, images, and Chinese text.

## 2025-06-14 21:40:00

### 1. Multi-Document Processing System v2.0 - Complete System Overhaul

**Change Type**: major feature + system architecture upgrade

> **Purpose**: Transform single-document PDF processor into comprehensive multi-document processing system with unified file management and project-based organization.

> **Detailed Description**: 
> 1. **Multi-Document Format Support**: Added comprehensive Word document processing (.docx/.doc) alongside existing PDF capabilities
> 2. **Unified File Management System**: Implemented `file_manager.py` with intelligent naming conventions, project-based organization, and standardized file structures
> 3. **Enhanced PDF Processing**: Upgraded `pdf_extractor.py` with better margin control, improved image extraction, and project integration
> 4. **Word Document Processing**: Created `word_extractor.py` supporting text, image, and table extraction from Word documents
> 5. **Unified Document Processor**: Developed `document_processor.py` as the main interface supporting all document formats with batch processing capabilities
> 6. **Project-Based Organization**: Each document creates independent project with dedicated directories for content, images, cache, and logs
> 7. **Intelligent Naming System**: Automatic filename sanitization, timestamp-based project naming, and standardized resource naming
> 8. **Batch Processing**: Support for directory-level batch processing of multiple documents
> 9. **Comprehensive Management**: Project listing, statistics, cleanup, and lifecycle management
> 10. **Enhanced Configuration**: Updated config system supporting new document processing parameters

> **Reason for Change**: 
> - Need to process multiple document formats beyond PDF
> - Original single-file approach caused file organization issues
> - Lack of proper naming conventions led to conflicts
> - Manual processing of multiple documents was inefficient
> - No centralized management of processing artifacts

> **Impact Scope**: Major system upgrade with new architecture while maintaining 100% backward compatibility with existing workflows.

> **API Changes**: 
> - New `DocumentProcessor` class for unified document handling
> - `FileManager` class for standardized file operations
> - Enhanced extractors with project-aware processing
> - Batch processing APIs for multiple documents
> - Project management and statistics APIs

> **Configuration Changes**: 
> - Added `BASE_DIR` for document workspace configuration
> - PDF margin control parameters (`PDF_TOP_MARGIN`, `PDF_BOTTOM_MARGIN`)
> - File naming control settings (`USE_TIMESTAMP`, `CLEAN_FILENAMES`)
> - Enhanced dependency management in requirements.txt

> **Performance Impact**: 
> - Project-based caching improves processing efficiency
> - Batch processing reduces overhead for multiple documents
> - Independent project spaces prevent resource conflicts
> - Intelligent naming prevents file collisions

```
NEW CORE MODULES:
- file_manager.py                  // Unified file naming and project management system
- document_processor.py            // Multi-document format unified processor  
- pdf_extractor.py                 // Enhanced PDF processor with project integration
- word_extractor.py                // Complete Word document processor (.docx/.doc)
- demo_multi_document.py           // Comprehensive system demonstration

NEW WORKSPACE STRUCTURE:
- documents/                       // Project-based workspace
  - source/                        // Original document storage
  - extracted/[project]/          // Project-specific extracted content
  - anki/                         // Generated Anki flashcards
  - cache/[project]/              // Project-specific cache
  - logs/                         // Processing logs

NEW DOCUMENTATION:
- README_v2.md                     // Comprehensive system documentation
- USAGE_GUIDE.md                   // Detailed usage instructions and best practices

ENHANCED CONFIGURATION:
- config.ini                       // Multi-document processing configuration
- requirements.txt                 // Updated dependencies with optional Word support

NAMING CONVENTIONS:
- Projects: {cleaned_filename}_{timestamp}
- Images: {project}_{source}_{page:03d}_img_{index:03d}.png
- Cache: chunk_{index:04d}.json
```

> **Migration Path**: Existing workflows remain fully functional. New features are opt-in through new CLI interface and APIs.

> **Testing**: Comprehensive demonstration script validates all features including PDF processing (2070 pages, 870 images), project management, batch processing, and naming conventions.

## 2025-06-14 20:00:00

### 1. Major Architecture Refactoring and Image Processing Enhancement

**Change Type**: major refactoring + feature enhancement

> **Purpose**: Improve code modularity, fix image processing issues, and enhance cross-chunk validation capabilities.

> **Detailed Description**: 
> 1. **Modular Architecture**: Extracted AI service logic into `ai_service.py` for better code reuse and maintainability
> 2. **Enhanced Image Processing**: Added smart cross-chunk image validation in `check_missing_images.py` to handle sliding window overlaps
> 3. **Selective Regeneration**: Implemented `regenerate_missing_chunks.py` for targeted chunk regeneration based on validation results
> 4. **Unified Anki Generation**: Created `anki_generator.py` as a reusable module for Anki card generation
> 5. **Improved AI Prompts**: Enhanced prompts with stronger emphasis on image preservation and processing

> **Reason for Change**: 
> - Original code had duplicated AI calling logic across multiple files
> - Image processing was failing due to incomplete questions spanning multiple chunks
> - Need for better validation and selective regeneration capabilities
> - Improved maintainability and code organization

> **Impact Scope**: Major refactoring affecting core processing pipeline and adding new validation capabilities.

> **API Changes**: 
> - Unified AI service interface with streaming support
> - Enhanced prompt engineering for better image handling
> - Cross-chunk validation API for comprehensive image checking

> **Configuration Changes**: 
> - Added support for emphasizing image processing in AI calls
> - Enhanced error handling and retry mechanisms
> - Improved logging and progress tracking

> **Performance Impact**: 
> - Reduced redundant processing through better caching
> - Selective regeneration saves time by only reprocessing problematic chunks
> - Cross-chunk validation prevents false positives in image missing detection

```
NEW FILES:
- ai_service.py                    // Unified AI service with streaming support
- anki_generator.py                // Modular Anki card generation utilities  
- check_missing_images.py          // Cross-chunk image validation tool
- regenerate_missing_chunks.py     // Selective chunk regeneration tool
- problematic_chunks.csv           // Validation reports

ENHANCED FILES:
- md_to_anki.py                    // Refactored to use modular AI service
- ankiflashcards.txt               // Improved with better image preservation
```

### 2. Image Processing Validation and Quality Assurance

**Change Type**: feature + bug fix

> **Purpose**: Ensure all images from PDF are properly preserved in Anki flashcards through comprehensive validation.

> **Detailed Description**: 
> - Implemented cross-chunk image validation to handle sliding window overlaps
> - Added CSV reporting for problematic chunks requiring regeneration
> - Enhanced AI prompts with stronger emphasis on image preservation
> - Created selective regeneration workflow for efficiency

> **Reason for Change**: Initial processing was losing images due to incomplete questions spanning multiple chunks, causing data loss in final Anki cards.

> **Impact Scope**: Significant improvement in data quality and completeness.

> **Results**: Successfully preserved 1950+ images across 4745 Q&A pairs, with 92/94 problematic chunks successfully regenerated.

## 2025-06-14 18:30:00

### 1. Add AI-powered Markdown to Anki conversion pipeline

**Change Type**: major feature

> **Purpose**: Provide intelligent conversion from Markdown Q&A content to Anki flashcards using AI processing.

> **Detailed Description**: Implemented `md_to_anki.py` with streaming AI API support for concurrent processing of large Markdown files. Features include: sliding window text chunking, concurrent AI API calls, automatic deduplication, error recovery, and progress tracking.

> **Reason for Change**: Automate the conversion of extracted PDF content (questions and answers) into Anki-ready flashcards, handling large files efficiently while maintaining quality.

> **Impact Scope**: Major addition - new core functionality for the processing pipeline.

> **API Changes**: Added OpenAI-compatible streaming API integration with configurable endpoints.

> **Configuration Changes**: Added comprehensive config.ini for API settings, processing parameters, and model selection.

> **Performance Impact**: Significant improvement - concurrent processing with caching and streaming API calls to handle large datasets efficiently.

```
root
- md_to_anki.py        // new core feature: AI-powered Markdown to Anki conversion
- config.ini           // new configuration management
- requirements.txt     // new dependency management
- test_*.py           // new comprehensive testing suite
- README.md           // new project documentation
- cache/              // new caching system for resume capability
```

## 2025-06-14 15:52:00

### 1. Add extract_pdf_md.py script

**Change Type**: feature

> **Purpose**: Provide a command-line tool to extract text and images from a PDF into Markdown.

> **Detailed Description**: Implemented `extract_pdf_md.py` using PyMuPDF (fitz) to extract text blocks within page margins and save images to an `images/` directory, then generate a Markdown file linking images.

> **Reason for Change**: Automate conversion of PDF content to Markdown for documentation and further processing.

> **Impact Scope**: Adds a new script; no modifications to existing files.

> **API Changes**: N/A

> **Configuration Changes**: N/A

> **Performance Impact**: Minimal; dependent on PyMuPDF extraction speed.

```
root
- extract_pdf_md.py   // new feature script for PDF extraction
- .codelf             // update project documentation templates
```