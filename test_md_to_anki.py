#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_md_to_anki.py

测试脚本：验证 AI 接口是否按照提示提取问答对
"""

import json
import os
from pathlib import Path
from md_to_anki import load_config, call_ai_api, create_ai_prompt

def test_ai_prompt():
    """测试 AI 指令创建"""
    print("=" * 60)
    print("测试 AI 指令创建")
    print("=" * 60)
    
    system_prompt, task_prompt = create_ai_prompt()
    print("系统指令:")
    print(system_prompt)
    print("\n任务指令:")
    print(task_prompt[:500] + "..." if len(task_prompt) > 500 else task_prompt)
    print()

def test_config_loading():
    """测试配置加载"""
    print("=" * 60)
    print("测试配置加载")
    print("=" * 60)
    
    try:
        cfg = load_config()
        print("✅ 配置加载成功")
        print(f"API 提供商: {cfg['api_provider']}")
        print(f"模型名称: {cfg['model_name']}")
        print(f"API 基础URL: {cfg['api_base_url']}")
        print(f"最大并发数: {cfg['max_workers']}")
        print(f"缓存目录: {cfg['cache_dir']}")
        return cfg
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return None

def test_ai_api():
    """测试 AI API 调用"""
    print("=" * 60)
    print("测试 AI API 调用")
    print("=" * 60)
    
    # 加载配置
    cfg = load_config()
    
    # 创建缓存目录
    cache_path = Path(cfg["cache_dir"])
    cache_path.mkdir(exist_ok=True)
    
    # 示例 Markdown 片段（更贴近实际数据）
    sample_chunk = """
1. 根据以下图形规律，下一个图形应该是？
![page_1_img_1.png](images/page_1_img_1.png)
A. 上图第一项
B. 上图第二项  
C. 上图第三项
D. 上图第四项
答：C
解：把线条分为上下移动和左右移动，横线从下往上移动1个单位，竖线从左向右移动1个单位（移到最右边就再回到最左边）

2. 空缺图形应该是？
专业笔试助攻\代做:jobhelp101
![page_2_img_1.png](images/page_2_img_1.png)
A. 上图第一项
B. 上图第二项
C. 上图第三项  
D. 上图第四项
答：D
解：图形1+2=3，第一排图形取最外层图形，第二排的图形为第一排外层图形里面的图形，第三排图形是第一排外层+第二排里面的图形
"""
    
    print(f"测试文本块长度: {len(sample_chunk)} 字符")
    print("正在调用 AI API...")
      # 调用 AI API
    success = call_ai_api(sample_chunk, cfg, 999)  # 使用999作为测试索引
    
    print(f"API 调用结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        # 从缓存文件中读取结果进行验证
        cache_file = cache_path / "999.json"
        try:
            with open(cache_file, "r", encoding="utf-8") as f:
                questions = json.load(f)
            
            print(f"\n从缓存文件提取的结果:")
            print(f"问答对数量: {len(questions)}")
            
            for i, qa in enumerate(questions, 1):
                print(f"\n问题 {i}:")
                print(f"Front: {qa.get('front', '')}")
                print(f"Back: {qa.get('back', '')}")
            
            # 保留缓存文件以供检查
            print(f"\n✅ 缓存文件保留在: {cache_file}")
            
        except FileNotFoundError:
            print(f"❌ 未找到缓存文件: {cache_file}")
        except json.JSONDecodeError as e:
            print(f"❌ 无法解析缓存文件: {e}")
        except Exception as e:
            print(f"❌ 读取缓存文件时发生错误: {e}")
    
    return success

def main():
    """主测试函数"""
    print("🚀 开始 Markdown to Anki 转换器测试")
    print()
    
    # 测试配置加载
    cfg = test_config_loading()
    if not cfg:
        print("❌ 配置测试失败，退出")
        return
    
    print()
    
    # 测试 AI 指令
    test_ai_prompt()
    
    print()
    
    # 测试 AI API
    api_success = test_ai_api()
    
    print()
    print("=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"配置加载: ✅ 成功")
    print(f"AI 指令创建: ✅ 成功")
    print(f"AI API 调用: {'✅ 成功' if api_success else '❌ 失败'}")
    
    if api_success:
        print("\n🎉 所有测试通过！系统准备就绪。")
        print("💡 建议：现在可以运行主脚本 `python md_to_anki.py`")
    else:
        print("\n⚠️  AI API 测试失败，请检查:")
        print("1. API 密钥是否正确")
        print("2. API 端点是否可访问")
        print("3. 模型名称是否正确")
        print("4. 网络连接是否正常")

if __name__ == "__main__":
    main()