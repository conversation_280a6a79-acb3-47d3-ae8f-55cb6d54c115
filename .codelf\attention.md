## Development Guidelines

### Framework and Language
> Analyze the framework and language choices for this project, focusing on best practices and standardization.

**Framework Considerations:**
- Library: PyMuPDF (fitz) compatibility with Python 3.x
- Version Compatibility: Ensure PyMuPDF version is tested against Python 3.7+
- API Usage: Use fitz-specific methods for text and image extraction efficiently
- AI API Integration: OpenAI-compatible streaming APIs for concurrent processing
- Dependency Management: Manage dependencies via requirements.txt or virtual environments

**Language Best Practices:**
- Compatibility: Support Python 3.7 and above
- Modern Features: Use f-strings, pathlib, and context managers
- Style Guide: Follow PEP 8 guidelines using tools like flake8 or black
- Documentation: Include docstrings for functions and modules, mention dependencies
- Async Processing: Use concurrent.futures for parallel AI API calls

### Code Abstraction and Reusability
> During development, prioritize code abstraction and reusability to ensure modular and component-based functionality. Try to search for existing solutions before reinventing the wheel.

**Modular Design Principles:**
- Single Responsibility: Each module handles one specific task (document extraction, AI processing, file generation, project management)
- High Cohesion, Low Coupling: Related functions are centralized, reducing dependencies between modules
- Stable Interfaces: Expose stable interfaces externally while internal implementations can vary
- Format Extensibility: New document formats can be added without affecting existing functionality

**Current Project Structure (v2.0 Multi-Document System):**
```
root
# Core Multi-Document Processing System
- document_processor.py           // Unified multi-document processor
- file_manager.py                 // Project-based file management system
- pdf_extractor.py                // Enhanced PDF processing module
- word_extractor.py               // Word document processing module
- demo_multi_document.py          // System demonstration and validation

# Legacy System (Fully Compatible)
- extract_pdf_md.py               // Legacy PDF extraction module
- ai_service.py                   // Unified AI service with streaming support  
- anki_generator.py               // Modular Anki card generation utilities
- md_to_anki.py                   // Main processing pipeline (refactored)
- check_missing_images.py         // Cross-chunk validation tool
- regenerate_missing_chunks.py    // Selective regeneration tool

# Project-Based Workspace
- documents/                      // Multi-document workspace
  - extracted/[project]/          // Project-specific content
  - cache/[project]/              // Project-specific cache
  - logs/                         // Processing logs
- config.ini                      // Enhanced configuration management
- test_*.py                       // Testing utilities
```

**v2.0 Architecture Benefits:**
- **Multi-Format Support**: Unified interface for PDF, Word, and extensible to other formats
- **Project Management**: Independent project spaces prevent conflicts and improve organization
- **Intelligent Naming**: Standardized naming conventions across all file types and projects
- **Batch Processing**: Efficient handling of multiple documents with progress tracking
- **Complete Backward Compatibility**: Legacy workflows remain fully functional
- **Enhanced File Organization**: Clear separation of concerns with project-based structure

### Coding Standards and Tools
**Code Formatting Tools:**
- flake8: Python code linting and style checking
- black: Python code formatting
- mypy: Static type checking (optional)

**Naming and Structure Conventions:**
- Semantic Naming: Variable/function names should clearly express their purpose
- Snake Case: Use snake_case for Python variables and functions
- Directory Structure follows functional responsibility division
- Configuration: Use config.ini for all configurable parameters
- Project Naming: `{cleaned_filename}_{timestamp}` format for unique identification
- Image Naming: `{project}_{source}_{page:03d}_img_{index:03d}.png` for standardization
- Cache Organization: Project-specific directories for isolation and cleanup

**Multi-Document Processing Standards:**
- Document Format Detection: Automatic format identification based on file extensions
- Unified Processing Interface: Common API across all document types
- Error Isolation: Document processing failures don't affect other documents
- Resource Management: Proper cleanup and memory management for large documents
- Progress Tracking: Comprehensive progress reporting for batch operations

### AI API Integration Standards
**API Design and Documentation:**
- OpenAI-compatible endpoints support
	* Use standard chat/completions format
	* Support both streaming and non-streaming modes
	* Unified AI service interface (`ai_service.py`)
- Comprehensive error handling
	* Retry mechanisms with exponential backoff
	* Graceful degradation for partial failures
	* Selective regeneration for failed chunks
- Caching strategy
	* Save processed chunks to avoid re-processing
	* Resume capability for interrupted processing
	* Backup system for regenerated content

**Enhanced Processing Features:**
- Cross-chunk validation
	* Smart image validation across sliding windows
	* Prevention of false positive missing image alerts
	* Comprehensive validation reporting via CSV
- Quality assurance improvements
	* Enhanced AI prompts for better image preservation
	* Cross-reference validation for content completeness
	* Selective regeneration based on validation results

**Data Flow:**
- Sliding window text processing
	* Configurable chunk size and stride for optimal AI processing
	* Overlap handling to prevent missing content at boundaries
- Concurrent processing
	* ThreadPoolExecutor for parallel API calls
	* Progress tracking with tqdm
- Quality assurance
	* JSON validation and repair
	* Content deduplication based on question similarity

### Performance and Security
**Performance Optimization Focus:**
- Streaming API calls
	* Reduce memory usage for large responses
	* Better handling of partial responses
- Multi-level caching mechanisms
	* Project-specific cache isolation
	* Resume interrupted processing from cache
	* Backup system for regenerated content
- Concurrent processing
	* Configurable worker threads for API calls
	* Rate limiting and retry logic
	* Batch processing optimization for multiple documents
- Memory Management
	* Efficient handling of large documents (2000+ pages)
	* Progressive processing to minimize memory footprint
	* Proper resource cleanup after processing

**Security Measures:**
- API key protection
	* Load from environment variables or secure config
	* Never hardcode API keys in source code
- Input validation
	* Validate configuration parameters
	* Sanitize file paths and user inputs
	* Document format verification before processing
- Error information protection
	* Log sensitive information appropriately
	* Avoid exposing API keys in error messages
- File System Security
	* Controlled access to project directories
	* Secure temporary file handling
	* Path traversal prevention

### Multi-Document Processing Architecture

**Document Format Support:**
- PDF Processing: Enhanced with project integration and improved image extraction
- Word Processing: Full support for .docx/.doc with text, images, and table extraction
- Extensible Architecture: Easy to add new document formats (PowerPoint, Excel, etc.)
- Format Detection: Automatic document type identification and processor selection

**Project Management Principles:**
- Project Isolation: Each document creates independent workspace preventing conflicts
- Resource Organization: Structured directories for content, images, cache, and logs
- Naming Standardization: Consistent naming across all file types and projects
- Lifecycle Management: Complete project creation, processing, and cleanup workflows

**Batch Processing Design:**
- Directory Scanning: Recursive document discovery with format filtering
- Parallel Processing: Concurrent document processing with progress tracking
- Error Handling: Individual document failures don't stop batch processing
- Result Aggregation: Comprehensive reporting of batch processing results

**File Management System:**
- Intelligent Naming: Automatic filename sanitization and conflict resolution
- Path Management: Centralized path generation and validation
- Storage Organization: Logical separation of different file types and purposes
- Cleanup Utilities: Automated cleanup with selective retention policies

### Document Format Processing Standards

**Word Document Enhancement Principles:**
- Format Preservation: Maintain original document formatting (colors, bold, italic) in Markdown output
- Precise Image Placement: Images should appear exactly where they exist in the original document
- Context Maintenance: Preserve relationship between images and surrounding text
- Naming Convention: Use file-specific prefixes for images to enable mixed document processing

**Image Processing Best Practices:**
- Single-Pass Extraction: Extract images only once to prevent duplication
- Contextual Placement: Insert images at their exact location in text flow
- Standardized References: Use HTML img tags with consistent format
- File-Specific Naming: Include source document name in image filenames
- Format Support: Handle multiple image formats (PNG, JPEG, GIF, BMP, TIFF)

**Text Format Conversion Standards:**
- Color Preservation: Convert Word colors to HTML span tags with RGB values
- Style Maintenance: Preserve bold, italic, and other text formatting
- HTML Integration: Use appropriate HTML tags within Markdown for complex formatting
- Error Handling: Graceful degradation when format extraction fails

**Quality Assurance Guidelines:**
- Eliminate Redundancy: Avoid duplicate content sections (e.g., "其他图片")
- Maintain Context: Ensure images appear with relevant text content
- Validate Extraction: Verify all images and formatting are properly extracted
- Error Recovery: Implement fallback mechanisms for processing failures