#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
word_extractor.py

Word文档内容提取器，将Word文档转换为Markdown格式
支持文本、图片、表格的提取
"""

import os
import sys
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import zipfile
import xml.etree.ElementTree as ET
from io import BytesIO

try:
    from docx import Document
    from docx.shared import Inches
    from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    logging.warning("python-docx 未安装，Word功能将受限")

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    logging.warning("Pillow 未安装，图片处理功能将受限")

from file_manager import FileManager


class WordExtractor:
    """Word文档内容提取器"""
    
    def __init__(self, file_manager: FileManager):
        """
        初始化Word提取器
        
        Args:
            file_manager: 文件管理器实例
        """
        self.file_manager = file_manager
        
        if not DOCX_AVAILABLE:
            raise ImportError("需要安装 python-docx: pip install python-docx")
    
    def extract_from_word(self, word_path: str) -> Dict[str, str]:
        """
        从Word文档提取内容到Markdown
        
        Args:
            word_path: Word文档路径
            
        Returns:
            包含提取结果的字典
        """
        if not Path(word_path).exists():
            raise FileNotFoundError(f"Word文档不存在: {word_path}")
        
        # 检查文件格式
        file_ext = Path(word_path).suffix.lower()
        if file_ext == '.doc':
            raise ValueError(f"不支持 .doc 格式文件，请将文件转换为 .docx 格式: {word_path}")
        elif file_ext != '.docx':
            raise ValueError(f"不支持的Word文件格式: {file_ext}")
        
        # 获取文件路径信息
        paths = self.file_manager.get_extraction_paths(word_path)
        project_name = paths['project_name']
        
        # 设置日志
        self._setup_project_logging(paths['log_file'])
        
        logging.info(f"开始提取Word文档: {word_path}")
        logging.info(f"项目名称: {project_name}")
        
        try:
            # 打开Word文档
            doc = Document(word_path)
            
            # 提取内容
            markdown_content = []
            image_count = 0            # 处理段落
            for para_idx, paragraph in enumerate(doc.paragraphs):
                # 处理段落文本（支持格式化）
                text = paragraph.text.strip()
                if text:
                    # 根据段落样式转换为Markdown，不再传递纯文本
                    md_text = self._convert_paragraph_to_markdown(paragraph, text)
                    markdown_content.append(md_text)
                  # 检查段落中的图片并在当前位置插入图片引用
                paragraph_images = self._extract_paragraph_images(
                    paragraph, paths['images_dir'], project_name, para_idx, image_count, word_path
                )
                # 在当前段落后插入图片引用
                for img_info in paragraph_images:
                    img_tag = f'<img alt="undefined" src="images/{img_info["filename"]}">'
                    markdown_content.append(img_tag)
                    markdown_content.append("")  # 添加空行
                    image_count += 1            # 处理表格
            for table_idx, table in enumerate(doc.tables):
                table_md = self._convert_table_to_markdown(table)
                if table_md:
                    markdown_content.append(f"\n## 表格 {table_idx + 1}\n")
                    markdown_content.append(table_md)
                    markdown_content.append("")
            
            # 写入Markdown文件
            final_content = "\n".join(markdown_content)
            with open(paths['markdown_file'], 'w', encoding='utf-8') as f:
                f.write(final_content)
            
            result = {
                'project_name': project_name,
                'markdown_file': str(paths['markdown_file']),
                'images_dir': str(paths['images_dir']),
                'image_count': image_count,
                'paragraph_count': len(doc.paragraphs),
                'table_count': len(doc.tables),
                'content_length': len(final_content)
            }
            
            logging.info(f"Word文档提取完成:")
            logging.info(f"  - Markdown文件: {paths['markdown_file']}")
            logging.info(f"  - 段落数: {result['paragraph_count']}")
            logging.info(f"  - 表格数: {result['table_count']}")
            logging.info(f"  - 图片数: {result['image_count']}")
            logging.info(f"  - 内容长度: {result['content_length']} 字符")
            
            return result
            
        except Exception as e:
            logging.error(f"提取Word文档失败: {e}")
            raise e
    
    def _convert_paragraph_to_markdown(self, paragraph, text: str) -> str:
        """
        将Word段落转换为Markdown格式，支持格式化文本
        
        Args:
            paragraph: Word段落对象
            text: 段落文本
            
        Returns:
            Markdown格式的文本
        """
        # 检查段落样式
        style_name = paragraph.style.name.lower() if paragraph.style else ""
        
        # 标题转换
        if 'heading' in style_name:
            if 'heading 1' in style_name:
                return f"# {text}\n"
            elif 'heading 2' in style_name:
                return f"## {text}\n"
            elif 'heading 3' in style_name:
                return f"### {text}\n"
            elif 'heading 4' in style_name:
                return f"#### {text}\n"
            elif 'heading 5' in style_name:
                return f"##### {text}\n"
            elif 'heading 6' in style_name:
                return f"###### {text}\n"
        
        # 列表项
        if paragraph.style and paragraph.style.name in ["List Paragraph", "ListParagraph"]:
            return f"- {text}\n"
        
        # 引用
        if 'quote' in style_name:
            return f"> {text}\n"
        
        # 处理运行级别的格式化
        formatted_text = self._format_paragraph_runs(paragraph)
        
        # 普通段落
        return f"{formatted_text}\n"
    
    def _format_paragraph_runs(self, paragraph) -> str:
        """
        处理段落中运行级别的格式化（加粗、颜色等）
        
        Args:
            paragraph: Word段落对象
            
        Returns:
            格式化后的文本
        """
        formatted_parts = []
        
        for run in paragraph.runs:
            text = run.text
            if not text:
                continue
              # 检查颜色
            color = None
            if run.font.color and run.font.color.rgb:
                # 将颜色转换为RGB格式
                rgb = run.font.color.rgb
                try:
                    # python-docx中RGBColor对象的属性可能是这样的
                    if hasattr(rgb, 'red'):
                        color = f"rgb({rgb.red}, {rgb.green}, {rgb.blue})"
                    else:
                        # 尝试其他可能的属性名
                        color = f"rgb({rgb[0]}, {rgb[1]}, {rgb[2]})"
                except (AttributeError, TypeError, IndexError):
                    # 如果无法获取RGB值，尝试转换为十六进制
                    try:
                        color_val = str(rgb)
                        if color_val and color_val != "000000":
                            # 转换十六进制到RGB
                            r = int(color_val[0:2], 16) if len(color_val) >= 2 else 0
                            g = int(color_val[2:4], 16) if len(color_val) >= 4 else 0
                            b = int(color_val[4:6], 16) if len(color_val) >= 6 else 0
                            color = f"rgb({r}, {g}, {b})"
                    except:
                        color = None
            
            # 检查加粗
            is_bold = run.font.bold
            
            # 应用格式
            if color and color != "rgb(0, 0, 0)":  # 非黑色文本
                if is_bold:
                    formatted_text = f'<span style="color:{color}"><strong>{text}</strong></span>'
                else:
                    formatted_text = f'<span style="color:{color}">{text}</span>'
            elif is_bold:
                formatted_text = f'**{text}**'
            else:
                formatted_text = text
            
            formatted_parts.append(formatted_text)
        
        return ''.join(formatted_parts)
    
    def _convert_table_to_markdown(self, table) -> str:
        """
        将Word表格转换为Markdown表格
        
        Args:
            table: Word表格对象
            
        Returns:
            Markdown格式的表格
        """
        if not table.rows:
            return ""
        
        markdown_rows = []
        
        # 处理表格行
        for row_idx, row in enumerate(table.rows):
            cells = []
            for cell in row.cells:
                # 清理单元格文本
                cell_text = cell.text.strip().replace('\n', ' ').replace('|', '\\|')
                cells.append(cell_text)            # 添加表格行
            markdown_rows.append(f"| {' | '.join(cells)} |")
            
            # 添加表头分隔符（第一行后）
            if row_idx == 0:
                separator = f"| {' | '.join(['---'] * len(cells))} |"
                markdown_rows.append(separator)
        
        return "\n".join(markdown_rows) + "\n"
    
    def _extract_paragraph_images(self, paragraph, images_dir: Path, 
                                 project_name: str, para_idx: int, current_image_count: int, word_path: str) -> List[Dict]:
        """
        从段落中提取图片
        
        Args:
            paragraph: Word段落对象
            images_dir: 图片保存目录
            project_name: 项目名称
            para_idx: 段落索引
            current_image_count: 当前已提取的图片数量
            word_path: Word文档路径
            
        Returns:
            提取的图片信息列表
        """
        extracted_images = []
        
        try:
            # 检查段落中的运行元素
            for run in paragraph.runs:
                # 查找图片元素
                for drawing in run.element.findall('.//{http://schemas.openxmlformats.org/wordprocessingml/2006/main}drawing'):
                    # 查找图片的blip元素
                    for blip in drawing.findall('.//{http://schemas.openxmlformats.org/drawingml/2006/main}blip'):
                        embed_id = blip.get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed')
                        if embed_id:
                            # 从document关系中获取图片信息
                            try:
                                image_part = paragraph._parent.part.related_parts[embed_id]
                                if hasattr(image_part, 'blob'):
                                    # 生成图片文件名（加上文件名前缀）
                                    img_ext = self._get_image_extension(image_part.content_type)
                                    base_name = Path(word_path).stem  # 获取文件名(不含扩展名)
                                    img_name = f"{base_name}_page_{para_idx + 1}_img_{len(extracted_images) + 1}{img_ext}"
                                    
                                    # 保存图片
                                    img_path = images_dir / img_name
                                    with open(img_path, 'wb') as img_file:
                                        img_file.write(image_part.blob)
                                    
                                    extracted_images.append({
                                        'filename': img_name,
                                        'path': str(img_path),
                                        'paragraph_index': para_idx
                                    })
                                    
                                    logging.info(f"提取段落图片: {img_name} (段落 {para_idx + 1})")
                                    
                            except (KeyError, AttributeError) as e:
                                logging.warning(f"无法提取嵌入图片 {embed_id}: {e}")
                    
        except Exception as e:
            logging.warning(f"段落 {para_idx} 图片提取失败: {e}")
        
        return extracted_images
    
    def _extract_remaining_document_images(self, word_path: str, images_dir: Path, 
                                         project_name: str, current_image_count: int) -> List[Dict]:
        """
        从Word文档中提取剩余的图片（没有被段落处理捕获的）
        
        Args:
            word_path: Word文档路径
            images_dir: 图片保存目录
            project_name: 项目名称
            current_image_count: 当前已提取的图片数量
            
        Returns:
            提取的图片信息列表
        """
        extracted_images = []
        
        try:
            # Word文档实际是一个ZIP文件
            with zipfile.ZipFile(word_path, 'r') as zip_file:
                # 查找媒体文件
                media_files = [f for f in zip_file.namelist() if f.startswith('word/media/')]
                
                for idx, media_file in enumerate(media_files):
                    try:
                        # 获取文件扩展名
                        file_ext = Path(media_file).suffix.lower()
                        if file_ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff']:
                            # 生成图片文件名
                            img_name = f"document_img_{current_image_count + idx + 1}{file_ext}"
                            
                            # 提取图片
                            with zip_file.open(media_file) as media_data:
                                img_path = images_dir / img_name
                                with open(img_path, 'wb') as img_file:
                                    img_file.write(media_data.read())
                            
                            extracted_images.append({
                                'filename': img_name,
                                'path': str(img_path),
                                'source': 'document_media'
                            })
                            
                            logging.info(f"提取文档图片: {img_name}")
                            
                    except Exception as e:
                        logging.warning(f"提取图片 {media_file} 失败: {e}")
                        
        except Exception as e:
            logging.warning(f"批量图片提取失败: {e}")
        
        return extracted_images
    
    def _setup_project_logging(self, log_file: Path):
        """
        为项目设置独立的日志记录
        
        Args:
            log_file: 日志文件路径
        """
        # 创建项目专用的logger
        project_logger = logging.getLogger(f"word_extractor_{log_file.stem}")
        project_logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if not project_logger.handlers:
            fmt = logging.Formatter("%(asctime)s [%(levelname)s] %(message)s")
            
            # 文件处理器
            fh = logging.FileHandler(log_file, encoding="utf-8")
            fh.setLevel(logging.DEBUG)
            fh.setFormatter(fmt)
            project_logger.addHandler(fh)
    
    def _get_image_extension(self, content_type: str) -> str:
        """
        根据内容类型获取图片扩展名
        
        Args:
            content_type: MIME类型
            
        Returns:
            图片扩展名
        """
        type_map = {
            'image/png': '.png',
            'image/jpeg': '.jpg',
            'image/jpg': '.jpg',
            'image/gif': '.gif',
            'image/bmp': '.bmp',
            'image/tiff': '.tiff'
        }
        return type_map.get(content_type, '.png')


def extract_word_to_markdown(word_path: str, base_dir: str = "documents") -> Dict[str, str]:
    """
    便捷函数：将Word文档转换为Markdown
    
    Args:
        word_path: Word文档路径
        base_dir: 基础工作目录
        
    Returns:
        提取结果字典
    """
    file_manager = FileManager(base_dir)
    extractor = WordExtractor(file_manager)
    return extractor.extract_from_word(word_path)


def main():
    """测试Word提取功能"""
    if len(sys.argv) < 2:
        print("使用方法: python word_extractor.py <word_file_path> [base_dir]")
        sys.exit(1)
    
    word_path = sys.argv[1]
    base_dir = sys.argv[2] if len(sys.argv) > 2 else "documents"
    
    # 设置基础日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s'
    )
    
    try:
        result = extract_word_to_markdown(word_path, base_dir)
        print("\n提取完成!")
        print(f"项目名称: {result['project_name']}")
        print(f"Markdown文件: {result['markdown_file']}")
        print(f"图片数量: {result['image_count']}")
        print(f"段落数: {result['paragraph_count']}")
        print(f"表格数: {result['table_count']}")
        
    except Exception as e:
        print(f"提取失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
