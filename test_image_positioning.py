#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_image_positioning.py

测试PDF图片位置优化功能
"""

import sys
import os
import logging
from pathlib import Path

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pdf_extractor import PDFExtractor
from file_manager import FileManager


def test_image_positioning(pdf_path: str, output_dir: str = "test_output"):
    """
    测试图片位置分析功能
    
    Args:
        pdf_path: PDF文件路径
        output_dir: 输出目录
    """
    print(f"开始测试PDF图片位置分析: {pdf_path}")
    
    # 设置日志
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler('image_positioning_test.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    try:
        # 创建文件管理器和PDF提取器
        file_manager = FileManager(output_dir)
        extractor = PDFExtractor(file_manager)
        
        # 执行提取
        result = extractor.extract_from_pdf(pdf_path)
        
        print("\n=== 提取结果 ===")
        print(f"项目名称: {result['project_name']}")
        print(f"Markdown文件: {result['markdown_file']}")
        print(f"图片目录: {result['images_dir']}")
        print(f"总页数: {result['total_pages']}")
        print(f"图片数量: {result['image_count']}")
        print(f"文本长度: {result['text_length']} 字符")
        
        # 检查生成的文件
        md_file = Path(result['markdown_file'])
        if md_file.exists():
            print(f"\nMarkdown文件大小: {md_file.stat().st_size} 字节")
            
            # 显示前几行内容
            with open(md_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()[:20]
                print("\n=== Markdown文件预览（前20行）===")
                for i, line in enumerate(lines, 1):
                    print(f"{i:2d}: {line.rstrip()}")
        
        # 检查图片文件
        images_dir = Path(result['images_dir'])
        if images_dir.exists():
            image_files = list(images_dir.glob("*.png")) + list(images_dir.glob("*.jpg"))
            print(f"\n=== 提取的图片文件 ===")
            for img_file in image_files:
                size = img_file.stat().st_size
                print(f"  {img_file.name}: {size:,} 字节")
        
        print("\n测试完成！")
        return result
        
    except Exception as e:
        print(f"测试失败: {e}")
        logging.error(f"测试失败: {e}", exc_info=True)
        return None


def demonstrate_position_analysis():
    """
    演示位置分析功能
    """
    print("=== PDF图片位置分析功能演示 ===")
    print()
    print("该功能可以：")
    print("1. 分析PDF中图片和文本的边界框位置")
    print("2. 计算图片与周围文本的相对位置关系")
    print("3. 智能判断图片应该插入的位置（上方、下方、中间、内嵌等）")
    print("4. 生成更合理的Markdown布局")
    print()
    print("位置关系类型：")
    print("  - between_paragraphs: 图片位于两段文字之间")
    print("  - after_paragraph: 图片位于某段文字下方")
    print("  - before_paragraph: 图片位于某段文字上方")
    print("  - inline: 图片与文字重叠（内嵌图片）")
    print("  - standalone: 独立位置的图片")
    print()


def main():
    """主函数"""
    if len(sys.argv) < 2:
        demonstrate_position_analysis()
        print("使用方法:")
        print("  python test_image_positioning.py <pdf_file_path> [output_dir]")
        print()
        print("示例:")
        print("  python test_image_positioning.py tiku.pdf")
        print("  python test_image_positioning.py document.pdf custom_output")
        return
    
    pdf_path = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else "test_output"
    
    if not Path(pdf_path).exists():
        print(f"错误: PDF文件不存在: {pdf_path}")
        return
    
    test_image_positioning(pdf_path, output_dir)


if __name__ == "__main__":
    main()
