#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
document_processor_gui.py

GUI应用程序，为document_processor.py提供用户友好的图形界面
支持所有命令行功能，包括单文档处理、批量处理、项目管理和Anki生成
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime

# 导入后端处理模块
from document_processor import DocumentProcessor
from file_manager import FileManager


class DocumentProcessorGUI:
    """文档处理器GUI主类"""
    
    def __init__(self):
        """初始化GUI应用程序"""
        self.root = tk.Tk()
        self.root.title("文档处理器 - Document Processor GUI")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # 设置应用程序图标（如果有的话）
        try:
            # 可以在这里设置应用程序图标
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
        
        # 创建消息队列用于线程间通信
        self.message_queue = queue.Queue()

        # 当前处理状态
        self.processing = False
        self.current_operation = None

        # 创建GUI组件
        self.create_widgets()
        self.setup_layout()
        self.setup_menu()

        # 初始化后端处理器（在GUI组件创建后）
        self.processor = None
        self.init_processor()

        # 启动消息处理循环
        self.process_queue()

        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 设置拖拽功能
        self.setup_drag_and_drop()

        # 加载设置
        self.load_settings_from_config()
    
    def init_processor(self):
        """初始化文档处理器"""
        try:
            self.processor = DocumentProcessor()
            self.log_message("文档处理器初始化成功")
        except Exception as e:
            self.log_message(f"文档处理器初始化失败: {e}", level="ERROR")
            messagebox.showerror("初始化错误", f"文档处理器初始化失败:\n{e}")
    
    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.main_frame)
        
        # 创建各个标签页
        self.create_single_doc_tab()
        self.create_batch_processing_tab()
        self.create_project_management_tab()
        self.create_markdown_to_anki_tab()
        self.create_settings_tab()
        
        # 创建状态栏和日志区域
        self.create_status_area()
    
    def create_single_doc_tab(self):
        """创建单文档处理标签页"""
        self.single_doc_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.single_doc_frame, text="单文档处理")
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(self.single_doc_frame, text="文件选择", padding=10)
        file_frame.pack(fill="x", padx=10, pady=5)
        
        self.selected_file_var = tk.StringVar()
        ttk.Label(file_frame, text="选择文档文件:").pack(anchor="w")
        
        file_select_frame = ttk.Frame(file_frame)
        file_select_frame.pack(fill="x", pady=5)
        
        self.file_entry = ttk.Entry(file_select_frame, textvariable=self.selected_file_var, width=60)
        self.file_entry.pack(side="left", fill="x", expand=True)
        
        ttk.Button(file_select_frame, text="浏览...", 
                  command=self.browse_single_file).pack(side="right", padx=(5, 0))
        
        # 处理选项
        options_frame = ttk.LabelFrame(self.single_doc_frame, text="处理选项", padding=10)
        options_frame.pack(fill="x", padx=10, pady=5)
        
        self.process_to_markdown_var = tk.BooleanVar()
        self.generate_anki_var = tk.BooleanVar()

        # 设置默认值
        self.process_to_markdown_var.set(True)
        self.generate_anki_var.set(False)

        ttk.Checkbutton(options_frame, text="提取为Markdown",
                       variable=self.process_to_markdown_var).pack(anchor="w")
        ttk.Checkbutton(options_frame, text="同时生成Anki闪卡",
                       variable=self.generate_anki_var).pack(anchor="w")
        
        # 处理按钮
        button_frame = ttk.Frame(self.single_doc_frame)
        button_frame.pack(fill="x", padx=10, pady=10)
        
        self.process_single_btn = ttk.Button(button_frame, text="开始处理", 
                                           command=self.process_single_document)
        self.process_single_btn.pack(side="left")
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(self.single_doc_frame, text="处理结果", padding=10)
        result_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        self.single_result_text = scrolledtext.ScrolledText(result_frame, height=10, wrap=tk.WORD)
        self.single_result_text.pack(fill="both", expand=True)
    
    def create_batch_processing_tab(self):
        """创建批量处理标签页"""
        self.batch_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.batch_frame, text="批量处理")
        
        # 目录选择区域
        dir_frame = ttk.LabelFrame(self.batch_frame, text="目录选择", padding=10)
        dir_frame.pack(fill="x", padx=10, pady=5)
        
        self.selected_dir_var = tk.StringVar()
        ttk.Label(dir_frame, text="选择包含文档的目录:").pack(anchor="w")
        
        dir_select_frame = ttk.Frame(dir_frame)
        dir_select_frame.pack(fill="x", pady=5)
        
        self.dir_entry = ttk.Entry(dir_select_frame, textvariable=self.selected_dir_var, width=60)
        self.dir_entry.pack(side="left", fill="x", expand=True)
        
        ttk.Button(dir_select_frame, text="浏览...", 
                  command=self.browse_directory).pack(side="right", padx=(5, 0))
        
        # 批量处理选项
        batch_options_frame = ttk.LabelFrame(self.batch_frame, text="批量处理选项", padding=10)
        batch_options_frame.pack(fill="x", padx=10, pady=5)
        
        self.recursive_var = tk.BooleanVar()
        self.skip_existing_var = tk.BooleanVar()

        # 设置默认值
        self.recursive_var.set(True)
        self.skip_existing_var.set(True)

        ttk.Checkbutton(batch_options_frame, text="递归处理子目录",
                       variable=self.recursive_var).pack(anchor="w")
        ttk.Checkbutton(batch_options_frame, text="跳过已处理的文件",
                       variable=self.skip_existing_var).pack(anchor="w")
        
        # 处理按钮
        batch_button_frame = ttk.Frame(self.batch_frame)
        batch_button_frame.pack(fill="x", padx=10, pady=10)
        
        self.process_batch_btn = ttk.Button(batch_button_frame, text="开始批量处理", 
                                          command=self.process_directory)
        self.process_batch_btn.pack(side="left")
        
        # 进度显示
        progress_frame = ttk.LabelFrame(self.batch_frame, text="处理进度", padding=10)
        progress_frame.pack(fill="x", padx=10, pady=5)
        
        self.batch_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.batch_progress.pack(fill="x", pady=5)
        
        self.batch_status_var = tk.StringVar(value="准备就绪")
        ttk.Label(progress_frame, textvariable=self.batch_status_var).pack(anchor="w")
        
        # 批量处理结果
        batch_result_frame = ttk.LabelFrame(self.batch_frame, text="处理结果", padding=10)
        batch_result_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        self.batch_result_text = scrolledtext.ScrolledText(batch_result_frame, height=8, wrap=tk.WORD)
        self.batch_result_text.pack(fill="both", expand=True)
    
    def create_project_management_tab(self):
        """创建项目管理标签页"""
        self.project_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.project_frame, text="项目管理")
        
        # 项目列表区域
        list_frame = ttk.LabelFrame(self.project_frame, text="项目列表", padding=10)
        list_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 搜索框
        search_frame = ttk.Frame(list_frame)
        search_frame.pack(fill="x", pady=(0, 10))
        
        ttk.Label(search_frame, text="搜索项目:").pack(side="left")
        self.project_search_var = tk.StringVar()
        self.project_search_var.trace_add("write", self.filter_projects)
        search_entry = ttk.Entry(search_frame, textvariable=self.project_search_var, width=30)
        search_entry.pack(side="left", padx=(5, 10))
        
        ttk.Button(search_frame, text="刷新列表", 
                  command=self.refresh_project_list).pack(side="left")
        
        # 项目列表树形控件
        columns = ("name", "type", "images", "status")
        self.project_tree = ttk.Treeview(list_frame, columns=columns, show="tree headings", height=12)
        
        # 设置列标题
        self.project_tree.heading("#0", text="项目名称")
        self.project_tree.heading("name", text="文件名")
        self.project_tree.heading("type", text="类型")
        self.project_tree.heading("images", text="图片数")
        self.project_tree.heading("status", text="状态")
        
        # 设置列宽
        self.project_tree.column("#0", width=300)
        self.project_tree.column("name", width=200)
        self.project_tree.column("type", width=80)
        self.project_tree.column("images", width=80)
        self.project_tree.column("status", width=120)
        
        # 添加滚动条
        project_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.project_tree.yview)
        self.project_tree.configure(yscrollcommand=project_scrollbar.set)
        
        self.project_tree.pack(side="left", fill="both", expand=True)
        project_scrollbar.pack(side="right", fill="y")
        
        # 项目操作按钮
        project_button_frame = ttk.Frame(self.project_frame)
        project_button_frame.pack(fill="x", padx=10, pady=5)

        ttk.Button(project_button_frame, text="查看详情",
                  command=self.view_project_details).pack(side="left", padx=(0, 5))
        ttk.Button(project_button_frame, text="打开文件夹",
                  command=self.open_project_folder).pack(side="left", padx=(0, 5))
        ttk.Button(project_button_frame, text="生成Anki闪卡",
                  command=self.generate_anki_for_selected_project).pack(side="left", padx=(0, 5))
        ttk.Button(project_button_frame, text="删除项目",
                  command=self.delete_project).pack(side="left", padx=(0, 5))
    
    def setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill="both", expand=True, padx=5, pady=5)
        self.notebook.pack(fill="both", expand=True)
    
    def setup_menu(self):
        """设置菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="打开文档...", command=self.browse_single_file)
        file_menu.add_command(label="打开目录...", command=self.browse_directory)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="清理缓存", command=self.clear_cache)
        tools_menu.add_command(label="导出项目列表", command=self.export_project_list)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)

    def create_markdown_to_anki_tab(self):
        """创建Markdown转Anki标签页"""
        self.md_anki_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.md_anki_frame, text="Markdown转Anki")

        # 说明文本
        info_frame = ttk.LabelFrame(self.md_anki_frame, text="功能说明", padding=10)
        info_frame.pack(fill="x", padx=10, pady=5)

        info_text = "此功能用于将任意Markdown文件直接转换为Anki闪卡，无需先处理为项目。\n适用于已有的Markdown笔记或从其他来源获得的Markdown文件。"
        ttk.Label(info_frame, text=info_text, wraplength=600).pack(anchor="w")

        # Markdown文件选择区域
        md_select_frame = ttk.LabelFrame(self.md_anki_frame, text="选择Markdown文件", padding=10)
        md_select_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(md_select_frame, text="Markdown文件路径:").pack(anchor="w")

        file_input_frame = ttk.Frame(md_select_frame)
        file_input_frame.pack(fill="x", pady=5)

        self.md_file_var = tk.StringVar()
        self.md_file_entry = ttk.Entry(file_input_frame, textvariable=self.md_file_var, width=60)
        self.md_file_entry.pack(side="left", fill="x", expand=True)

        ttk.Button(file_input_frame, text="浏览...",
                  command=self.browse_markdown_file).pack(side="right", padx=(5, 0))

        # 转换选项
        options_frame = ttk.LabelFrame(self.md_anki_frame, text="转换选项", padding=10)
        options_frame.pack(fill="x", padx=10, pady=5)

        self.preserve_images_var = tk.BooleanVar()
        self.preserve_images_var.set(True)  # 设置默认值
        ttk.Checkbutton(options_frame, text="保留图片引用",
                       variable=self.preserve_images_var).pack(anchor="w")

        # 转换按钮
        convert_button_frame = ttk.Frame(self.md_anki_frame)
        convert_button_frame.pack(fill="x", padx=10, pady=10)

        self.md_to_anki_btn = ttk.Button(convert_button_frame, text="开始转换",
                                       command=self.convert_markdown_to_anki)
        self.md_to_anki_btn.pack(side="left")

        # 转换结果
        result_frame = ttk.LabelFrame(self.md_anki_frame, text="转换结果", padding=10)
        result_frame.pack(fill="both", expand=True, padx=10, pady=5)

        self.md_anki_result_text = scrolledtext.ScrolledText(result_frame, height=10, wrap=tk.WORD)
        self.md_anki_result_text.pack(fill="both", expand=True)

    def create_settings_tab(self):
        """创建设置标签页"""
        self.settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_frame, text="设置")

        # AI设置区域
        ai_frame = ttk.LabelFrame(self.settings_frame, text="AI服务设置", padding=10)
        ai_frame.pack(fill="x", padx=10, pady=5)

        # API设置
        ttk.Label(ai_frame, text="API Base URL:").grid(row=0, column=0, sticky="w", pady=2)
        self.api_url_var = tk.StringVar()
        ttk.Entry(ai_frame, textvariable=self.api_url_var, width=50).grid(row=0, column=1, sticky="ew", padx=(5, 0), pady=2)

        # API Key设置（带显示/隐藏功能）
        ttk.Label(ai_frame, text="API Key:").grid(row=1, column=0, sticky="w", pady=2)

        api_key_frame = ttk.Frame(ai_frame)
        api_key_frame.grid(row=1, column=1, sticky="ew", padx=(5, 0), pady=2)

        self.api_key_var = tk.StringVar()
        self.api_key_show_var = tk.BooleanVar()
        self.api_key_show_var.set(False)  # 默认隐藏
        self.api_key_entry = ttk.Entry(api_key_frame, textvariable=self.api_key_var, width=45, show="*")
        self.api_key_entry.pack(side="left", fill="x", expand=True)

        self.show_key_btn = ttk.Button(api_key_frame, text="显示", width=6,
                                     command=self.toggle_api_key_visibility)
        self.show_key_btn.pack(side="right", padx=(5, 0))

        ttk.Label(ai_frame, text="模型名称:").grid(row=2, column=0, sticky="w", pady=2)
        self.model_name_var = tk.StringVar()
        ttk.Entry(ai_frame, textvariable=self.model_name_var, width=50).grid(row=2, column=1, sticky="ew", padx=(5, 0), pady=2)

        ai_frame.columnconfigure(1, weight=1)

        # 处理设置区域
        processing_frame = ttk.LabelFrame(self.settings_frame, text="处理设置", padding=10)
        processing_frame.pack(fill="x", padx=10, pady=5)

        ttk.Label(processing_frame, text="PDF顶部边距:").grid(row=0, column=0, sticky="w", pady=2)
        self.pdf_top_margin_var = tk.StringVar(value="50.0")
        ttk.Entry(processing_frame, textvariable=self.pdf_top_margin_var, width=20).grid(row=0, column=1, sticky="w", padx=(5, 0), pady=2)

        ttk.Label(processing_frame, text="PDF底部边距:").grid(row=1, column=0, sticky="w", pady=2)
        self.pdf_bottom_margin_var = tk.StringVar(value="50.0")
        ttk.Entry(processing_frame, textvariable=self.pdf_bottom_margin_var, width=20).grid(row=1, column=1, sticky="w", padx=(5, 0), pady=2)

        ttk.Label(processing_frame, text="最大并发数:").grid(row=2, column=0, sticky="w", pady=2)
        self.max_workers_var = tk.StringVar(value="10")
        ttk.Entry(processing_frame, textvariable=self.max_workers_var, width=20).grid(row=2, column=1, sticky="w", padx=(5, 0), pady=2)

        ttk.Label(processing_frame, text="文本块大小:").grid(row=3, column=0, sticky="w", pady=2)
        self.chunk_size_var = tk.StringVar(value="500")
        ttk.Entry(processing_frame, textvariable=self.chunk_size_var, width=20).grid(row=3, column=1, sticky="w", padx=(5, 0), pady=2)

        ttk.Label(processing_frame, text="文本块步长:").grid(row=4, column=0, sticky="w", pady=2)
        self.chunk_stride_var = tk.StringVar(value="450")
        ttk.Entry(processing_frame, textvariable=self.chunk_stride_var, width=20).grid(row=4, column=1, sticky="w", padx=(5, 0), pady=2)

        ttk.Label(processing_frame, text="跳过行数:").grid(row=5, column=0, sticky="w", pady=2)
        self.lines_to_skip_var = tk.StringVar(value="13")
        ttk.Entry(processing_frame, textvariable=self.lines_to_skip_var, width=20).grid(row=5, column=1, sticky="w", padx=(5, 0), pady=2)

        # 设置按钮
        settings_button_frame = ttk.Frame(self.settings_frame)
        settings_button_frame.pack(fill="x", padx=10, pady=10)

        ttk.Button(settings_button_frame, text="保存设置",
                  command=self.save_settings).pack(side="left", padx=(0, 10))
        ttk.Button(settings_button_frame, text="重置默认",
                  command=self.reset_settings).pack(side="left")

    def create_status_area(self):
        """创建状态栏和日志区域"""
        # 状态栏
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill="x", side="bottom")

        self.status_var = tk.StringVar(value="准备就绪")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var, relief="sunken")
        self.status_label.pack(side="left", fill="x", expand=True, padx=2, pady=2)

        # 进度条（隐藏状态）
        self.main_progress = ttk.Progressbar(self.status_frame, mode='indeterminate')
        # 不立即显示进度条

        # 日志区域（可折叠）
        self.log_frame = ttk.LabelFrame(self.root, text="日志", padding=5)
        # 默认不显示日志区域

        self.log_text = scrolledtext.ScrolledText(self.log_frame, height=6, wrap=tk.WORD)
        self.log_text.pack(fill="both", expand=True)

        # 日志控制按钮
        log_button_frame = ttk.Frame(self.log_frame)
        log_button_frame.pack(fill="x", pady=(5, 0))

        ttk.Button(log_button_frame, text="清空日志",
                  command=self.clear_log).pack(side="left")
        ttk.Button(log_button_frame, text="保存日志",
                  command=self.save_log).pack(side="left", padx=(5, 0))

        # 切换日志显示的按钮
        self.toggle_log_btn = ttk.Button(self.status_frame, text="显示日志",
                                       command=self.toggle_log_display)
        self.toggle_log_btn.pack(side="right", padx=2, pady=2)

        self.log_visible = False

    # ==================== 文件选择方法 ====================

    def browse_single_file(self):
        """浏览选择单个文档文件"""
        filetypes = [
            ("所有支持的文档", "*.pdf;*.docx;*.doc;*.md;*.txt"),
            ("PDF文件", "*.pdf"),
            ("Word文档", "*.docx;*.doc"),
            ("Markdown文件", "*.md"),
            ("文本文件", "*.txt"),
            ("所有文件", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title="选择要处理的文档文件",
            filetypes=filetypes
        )

        if filename:
            self.selected_file_var.set(filename)
            self.log_message(f"选择文件: {filename}")

    def browse_directory(self):
        """浏览选择目录"""
        directory = filedialog.askdirectory(
            title="选择包含文档的目录"
        )

        if directory:
            self.selected_dir_var.set(directory)
            self.log_message(f"选择目录: {directory}")

    def browse_markdown_file(self):
        """浏览选择Markdown文件"""
        filename = filedialog.askopenfilename(
            title="选择Markdown文件",
            filetypes=[("Markdown文件", "*.md"), ("所有文件", "*.*")]
        )

        if filename:
            self.md_file_var.set(filename)
            self.log_message(f"选择Markdown文件: {filename}")

    # ==================== 处理方法 ====================

    def process_single_document(self):
        """处理单个文档"""
        if self.processing:
            messagebox.showwarning("处理中", "已有处理任务在进行中，请等待完成")
            return

        file_path = self.selected_file_var.get().strip()
        if not file_path:
            messagebox.showerror("错误", "请先选择要处理的文档文件")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", f"文件不存在: {file_path}")
            return

        # 清空结果显示
        self.single_result_text.delete(1.0, tk.END)

        # 启动处理线程
        self.start_processing("single_document", {
            'file_path': file_path,
            'to_markdown': self.process_to_markdown_var.get(),
            'generate_anki': self.generate_anki_var.get()
        })

    def process_directory(self):
        """批量处理目录"""
        if self.processing:
            messagebox.showwarning("处理中", "已有处理任务在进行中，请等待完成")
            return

        dir_path = self.selected_dir_var.get().strip()
        if not dir_path:
            messagebox.showerror("错误", "请先选择要处理的目录")
            return

        if not os.path.exists(dir_path):
            messagebox.showerror("错误", f"目录不存在: {dir_path}")
            return

        # 清空结果显示
        self.batch_result_text.delete(1.0, tk.END)
        self.batch_progress['value'] = 0
        self.batch_status_var.set("准备开始批量处理...")

        # 启动处理线程
        self.start_processing("batch_process", {
            'dir_path': dir_path,
            'recursive': self.recursive_var.get(),
            'skip_existing': self.skip_existing_var.get()
        })

    def generate_anki_cards(self):
        """生成Anki闪卡"""
        if self.processing:
            messagebox.showwarning("处理中", "已有处理任务在进行中，请等待完成")
            return

        project_name = self.anki_project_var.get().strip()
        if not project_name:
            messagebox.showerror("错误", "请输入项目名称")
            return

        if len(project_name) < 5:
            messagebox.showerror("错误", "项目名称至少需要5个字符以支持模糊匹配")
            return

        # 清空结果显示
        self.anki_result_text.delete(1.0, tk.END)

        # 启动处理线程
        self.start_processing("generate_anki", {
            'project_name': project_name,
            'interactive': self.interactive_mode_var.get()
        })

    def convert_markdown_to_anki(self):
        """直接转换Markdown为Anki"""
        if self.processing:
            messagebox.showwarning("处理中", "已有处理任务在进行中，请等待完成")
            return

        md_file = self.md_file_var.get().strip()
        if not md_file:
            messagebox.showerror("错误", "请先选择Markdown文件")
            return

        if not os.path.exists(md_file):
            messagebox.showerror("错误", f"文件不存在: {md_file}")
            return

        # 清空结果显示
        self.anki_result_text.delete(1.0, tk.END)

        # 启动处理线程
        self.start_processing("md_to_anki", {
            'md_file': md_file
        })

    # ==================== 线程处理方法 ====================

    def start_processing(self, operation_type: str, params: Dict[str, Any]):
        """启动处理线程"""
        self.processing = True
        self.current_operation = operation_type

        # 显示进度条
        self.show_progress()

        # 禁用相关按钮
        self.disable_processing_buttons()

        # 启动工作线程
        thread = threading.Thread(
            target=self.processing_worker,
            args=(operation_type, params),
            daemon=True
        )
        thread.start()

    def processing_worker(self, operation_type: str, params: Dict[str, Any]):
        """处理工作线程"""
        try:
            if operation_type == "single_document":
                result = self.process_single_document_worker(params)
            elif operation_type == "batch_process":
                result = self.process_directory_worker(params)
            elif operation_type == "generate_anki":
                result = self.generate_anki_worker(params)
            elif operation_type == "md_to_anki":
                result = self.md_to_anki_worker(params)
            else:
                raise ValueError(f"未知的操作类型: {operation_type}")

            # 发送成功结果到主线程
            self.message_queue.put(("success", operation_type, result))

        except Exception as e:
            # 发送错误结果到主线程
            self.message_queue.put(("error", operation_type, str(e)))

    def process_single_document_worker(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """单文档处理工作函数"""
        file_path = params['file_path']
        to_markdown = params['to_markdown']
        generate_anki = params['generate_anki']

        self.message_queue.put(("status", "正在处理文档...", None))

        if to_markdown:
            # 处理文档为Markdown
            result = self.processor.process_document(file_path)

            if generate_anki:
                # 同时生成Anki
                self.message_queue.put(("status", "正在生成Anki闪卡...", None))
                anki_result = self.processor.generate_anki_for_project(
                    result['project_name'],
                    interactive=False  # GUI模式下使用非交互式
                )
                result['anki_result'] = anki_result

        return result

    def process_directory_worker(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """批量处理工作函数"""
        dir_path = params['dir_path']
        recursive = params['recursive']
        skip_existing = params['skip_existing']

        self.message_queue.put(("status", "扫描目录中的文档...", None))

        # 扫描文档文件
        supported_extensions = {'.pdf', '.docx', '.doc', '.md', '.txt'}
        files_to_process = []

        if recursive:
            for root, _, files in os.walk(dir_path):
                for file in files:
                    if Path(file).suffix.lower() in supported_extensions:
                        files_to_process.append(os.path.join(root, file))
        else:
            for file in os.listdir(dir_path):
                file_path = os.path.join(dir_path, file)
                if os.path.isfile(file_path) and Path(file).suffix.lower() in supported_extensions:
                    files_to_process.append(file_path)

        if not files_to_process:
            raise ValueError("在指定目录中未找到支持的文档文件")

        self.message_queue.put(("batch_total", len(files_to_process), None))

        results = []
        for i, file_path in enumerate(files_to_process):
            try:
                self.message_queue.put(("status", f"处理文件 {i+1}/{len(files_to_process)}: {os.path.basename(file_path)}", None))
                self.message_queue.put(("batch_progress", i, None))

                # 检查是否跳过已处理的文件
                if skip_existing:
                    # 这里可以添加检查逻辑
                    pass

                result = self.processor.process_document(file_path)
                results.append(result)

            except Exception as e:
                error_result = {
                    'original_file': file_path,
                    'error': str(e)
                }
                results.append(error_result)

        self.message_queue.put(("batch_progress", len(files_to_process), None))

        return {
            'total_files': len(files_to_process),
            'results': results,
            'success_count': len([r for r in results if 'error' not in r]),
            'error_count': len([r for r in results if 'error' in r])
        }

    def generate_anki_worker(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Anki生成工作函数"""
        project_name = params['project_name']
        # interactive = params['interactive']  # GUI模式下总是使用非交互式

        self.message_queue.put(("status", f"搜索匹配项目: {project_name}", None))

        # 在GUI模式下，我们需要处理交互式选择
        result = self.processor.generate_anki_for_project(
            project_name,
            interactive=False  # GUI模式下总是使用非交互式
        )

        return result

    def md_to_anki_worker(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Markdown转Anki工作函数"""
        md_file = params['md_file']

        self.message_queue.put(("status", f"处理Markdown文件: {os.path.basename(md_file)}", None))

        result = self.processor.process_markdown_to_anki(md_file)

        return result

    # ==================== UI更新方法 ====================

    def process_queue(self):
        """处理消息队列"""
        try:
            while True:
                message_type, data, extra = self.message_queue.get_nowait()

                if message_type == "status":
                    self.status_var.set(data)
                elif message_type == "batch_total":
                    self.batch_progress['maximum'] = data
                elif message_type == "batch_progress":
                    self.batch_progress['value'] = data
                    if data < self.batch_progress['maximum']:
                        self.batch_status_var.set(f"处理进度: {data}/{int(self.batch_progress['maximum'])}")
                    else:
                        self.batch_status_var.set("批量处理完成")
                elif message_type == "success":
                    self.handle_processing_success(data, extra)
                elif message_type == "error":
                    self.handle_processing_error(data, extra)

        except queue.Empty:
            pass

        # 继续处理队列
        self.root.after(100, self.process_queue)

    def handle_processing_success(self, operation_type: str, result: Dict[str, Any]):
        """处理成功结果"""
        self.processing = False
        self.hide_progress()
        self.enable_processing_buttons()
        self.status_var.set("处理完成")

        if operation_type == "single_document":
            self.display_single_document_result(result)
        elif operation_type == "batch_process":
            self.display_batch_process_result(result)
        elif operation_type == "generate_anki":
            self.display_anki_result(result, target="project")
        elif operation_type == "md_to_anki":
            self.display_anki_result(result, target="markdown")

        # 刷新项目列表
        self.refresh_project_list()

    def handle_processing_error(self, operation_type: str, error_message: str):
        """处理错误结果"""
        self.processing = False
        self.hide_progress()
        self.enable_processing_buttons()
        self.status_var.set("处理失败")

        self.log_message(f"处理失败: {error_message}", level="ERROR")
        messagebox.showerror("处理失败", f"操作失败:\n{error_message}")

    def display_single_document_result(self, result: Dict[str, Any]):
        """显示单文档处理结果"""
        text = self.single_result_text
        text.delete(1.0, tk.END)

        text.insert(tk.END, "✅ 文档处理完成!\n\n")
        text.insert(tk.END, f"项目名称: {result['project_name']}\n")
        text.insert(tk.END, f"文档类型: {result['document_type'].upper()}\n")
        text.insert(tk.END, f"原始文件: {result['original_file']}\n")
        text.insert(tk.END, f"Markdown文件: {result['markdown_file']}\n")

        if 'image_count' in result:
            text.insert(tk.END, f"图片数量: {result['image_count']}\n")
        if 'total_pages' in result:
            text.insert(tk.END, f"总页数: {result['total_pages']}\n")
        if 'paragraph_count' in result:
            text.insert(tk.END, f"段落数: {result['paragraph_count']}\n")

        if 'anki_result' in result:
            anki_result = result['anki_result']
            text.insert(tk.END, f"\n🎴 Anki闪卡生成完成!\n")
            text.insert(tk.END, f"输出文件: {anki_result['output_file']}\n")
            text.insert(tk.END, f"总卡片数: {anki_result['total_cards']}\n")
            text.insert(tk.END, f"包含图片的卡片: {anki_result['questions_with_images']}\n")
            text.insert(tk.END, f"图片总数: {anki_result['total_images']}\n")

    def display_batch_process_result(self, result: Dict[str, Any]):
        """显示批量处理结果"""
        text = self.batch_result_text
        text.delete(1.0, tk.END)

        text.insert(tk.END, f"📁 批量处理完成!\n\n")
        text.insert(tk.END, f"总文件数: {result['total_files']}\n")
        text.insert(tk.END, f"成功处理: {result['success_count']}\n")
        text.insert(tk.END, f"处理失败: {result['error_count']}\n\n")

        text.insert(tk.END, "处理详情:\n")
        text.insert(tk.END, "-" * 50 + "\n")

        for item in result['results']:
            if 'error' in item:
                text.insert(tk.END, f"❌ {os.path.basename(item['original_file'])}: {item['error']}\n")
            else:
                text.insert(tk.END, f"✅ {item['project_name']}: {item['document_type'].upper()}")
                if 'image_count' in item:
                    text.insert(tk.END, f" ({item['image_count']}张图片)")
                text.insert(tk.END, "\n")

    def display_anki_result(self, result: Dict[str, Any], target: str = "markdown"):
        """显示Anki生成结果"""
        if target == "markdown":
            text = self.md_anki_result_text
        else:
            # 如果是项目管理中生成的，显示在单文档处理结果中
            text = self.single_result_text

        text.delete(1.0, tk.END)

        text.insert(tk.END, "🎴 Anki闪卡生成完成!\n\n")
        text.insert(tk.END, f"项目名称: {result['project_name']}\n")
        text.insert(tk.END, f"输入文件: {result['input_file']}\n")
        text.insert(tk.END, f"输出文件: {result['output_file']}\n")
        text.insert(tk.END, f"缓存目录: {result['cache_dir']}\n\n")
        text.insert(tk.END, f"总卡片数: {result['total_cards']}\n")
        text.insert(tk.END, f"包含图片的卡片: {result['questions_with_images']}\n")
        text.insert(tk.END, f"图片总数: {result['total_images']}\n")
        text.insert(tk.END, f"源数据块: {result['source_chunks']}\n")

    # ==================== 项目管理方法 ====================

    def refresh_project_list(self):
        """刷新项目列表"""
        if not self.processor:
            return

        # 清空现有项目
        for item in self.project_tree.get_children():
            self.project_tree.delete(item)

        try:
            projects = self.processor.file_manager.list_projects()

            for project in projects:
                # 确定项目状态
                status_parts = []
                if project['has_images']:
                    status_parts.append(f"{project['image_count']}图片")
                if project['has_cache']:
                    status_parts.append("已缓存")
                if project['has_anki']:
                    status_parts.append("已生成Anki")

                status = ", ".join(status_parts) if status_parts else "仅Markdown"

                # 插入项目到树形控件
                self.project_tree.insert("", "end",
                                       text=project['name'],
                                       values=(
                                           os.path.basename(project['markdown_file']),
                                           "Mixed",  # 类型暂时显示为Mixed
                                           project['image_count'],
                                           status
                                       ))

            self.log_message(f"刷新项目列表完成，共 {len(projects)} 个项目")

        except Exception as e:
            self.log_message(f"刷新项目列表失败: {e}", level="ERROR")

    def filter_projects(self, *args):
        """过滤项目列表"""
        search_term = self.project_search_var.get().lower()

        # 如果搜索框为空，显示所有项目
        if not search_term:
            self.refresh_project_list()
            return

        # 清空现有显示
        for item in self.project_tree.get_children():
            self.project_tree.delete(item)

        try:
            projects = self.processor.file_manager.list_projects()

            # 过滤匹配的项目
            filtered_projects = [
                p for p in projects
                if search_term in p['name'].lower() or
                   search_term in os.path.basename(p['markdown_file']).lower()
            ]

            for project in filtered_projects:
                status_parts = []
                if project['has_images']:
                    status_parts.append(f"{project['image_count']}图片")
                if project['has_cache']:
                    status_parts.append("已缓存")
                if project['has_anki']:
                    status_parts.append("已生成Anki")

                status = ", ".join(status_parts) if status_parts else "仅Markdown"

                self.project_tree.insert("", "end",
                                       text=project['name'],
                                       values=(
                                           os.path.basename(project['markdown_file']),
                                           "Mixed",
                                           project['image_count'],
                                           status
                                       ))

        except Exception as e:
            self.log_message(f"过滤项目失败: {e}", level="ERROR")

    def search_projects_for_anki(self):
        """为Anki生成搜索项目"""
        search_term = self.anki_project_var.get().strip()
        if not search_term:
            messagebox.showwarning("警告", "请输入项目名称进行搜索")
            return

        try:
            projects = self.processor.file_manager.list_projects()

            # 模糊匹配项目
            matches = []
            for project in projects:
                if (search_term.lower() in project['name'].lower() or
                    project['name'].lower().startswith(search_term.lower())):
                    matches.append(project)

            if not matches:
                messagebox.showinfo("搜索结果", f"未找到匹配 '{search_term}' 的项目")
                return

            if len(matches) == 1:
                # 只有一个匹配项，直接设置
                self.anki_project_var.set(matches[0]['name'])
                messagebox.showinfo("搜索结果", f"找到匹配项目: {matches[0]['name']}")
            else:
                # 多个匹配项，显示选择对话框
                self.show_project_selection_dialog(matches)

        except Exception as e:
            self.log_message(f"搜索项目失败: {e}", level="ERROR")
            messagebox.showerror("错误", f"搜索项目失败: {e}")

    def show_project_selection_dialog(self, projects):
        """显示项目选择对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("选择项目")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 项目列表
        ttk.Label(dialog, text="找到多个匹配的项目，请选择一个:").pack(pady=10)

        listbox_frame = ttk.Frame(dialog)
        listbox_frame.pack(fill="both", expand=True, padx=10, pady=5)

        listbox = tk.Listbox(listbox_frame)
        scrollbar = ttk.Scrollbar(listbox_frame, orient="vertical", command=listbox.yview)
        listbox.configure(yscrollcommand=scrollbar.set)

        for project in projects:
            status_info = f" ({project['image_count']}图片)" if project['has_images'] else ""
            listbox.insert(tk.END, f"{project['name']}{status_info}")

        listbox.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill="x", padx=10, pady=10)

        def on_select():
            selection = listbox.curselection()
            if selection:
                selected_project = projects[selection[0]]
                self.anki_project_var.set(selected_project['name'])
                dialog.destroy()
            else:
                messagebox.showwarning("警告", "请选择一个项目")

        def on_cancel():
            dialog.destroy()

        ttk.Button(button_frame, text="选择", command=on_select).pack(side="right", padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=on_cancel).pack(side="right")

        # 双击选择
        listbox.bind("<Double-Button-1>", lambda e: on_select())

        # 居中显示对话框
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

    def view_project_details(self):
        """查看项目详情"""
        selection = self.project_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个项目")
            return

        project_name = self.project_tree.item(selection[0])['text']

        try:
            projects = self.processor.file_manager.list_projects()
            project = next((p for p in projects if p['name'] == project_name), None)

            if not project:
                messagebox.showerror("错误", "项目不存在")
                return

            # 显示项目详情对话框
            self.show_project_details_dialog(project)

        except Exception as e:
            self.log_message(f"查看项目详情失败: {e}", level="ERROR")
            messagebox.showerror("错误", f"查看项目详情失败: {e}")

    def show_project_details_dialog(self, project):
        """显示项目详情对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"项目详情 - {project['name']}")
        dialog.geometry("500x400")
        dialog.transient(self.root)

        # 创建详情文本
        text_frame = ttk.Frame(dialog)
        text_frame.pack(fill="both", expand=True, padx=10, pady=10)

        details_text = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD)
        details_text.pack(fill="both", expand=True)

        # 填充项目详情
        details_text.insert(tk.END, f"项目名称: {project['name']}\n")
        details_text.insert(tk.END, f"项目路径: {project['path']}\n")
        details_text.insert(tk.END, f"Markdown文件: {project['markdown_file']}\n")
        details_text.insert(tk.END, f"包含图片: {'是' if project['has_images'] else '否'}\n")
        details_text.insert(tk.END, f"图片数量: {project['image_count']}\n")
        details_text.insert(tk.END, f"已缓存: {'是' if project['has_cache'] else '否'}\n")
        details_text.insert(tk.END, f"已生成Anki: {'是' if project['has_anki'] else '否'}\n")

        details_text.config(state="disabled")

        # 关闭按钮
        ttk.Button(dialog, text="关闭", command=dialog.destroy).pack(pady=10)

    def open_project_folder(self):
        """打开项目文件夹"""
        selection = self.project_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个项目")
            return

        project_name = self.project_tree.item(selection[0])['text']

        try:
            projects = self.processor.file_manager.list_projects()
            project = next((p for p in projects if p['name'] == project_name), None)

            if not project:
                messagebox.showerror("错误", "项目不存在")
                return

            # 打开项目文件夹
            import subprocess
            import platform

            folder_path = project['path']

            if platform.system() == "Windows":
                subprocess.run(["explorer", folder_path])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])

        except Exception as e:
            self.log_message(f"打开项目文件夹失败: {e}", level="ERROR")
            messagebox.showerror("错误", f"打开项目文件夹失败: {e}")

    def delete_project(self):
        """删除项目"""
        selection = self.project_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个项目")
            return

        project_name = self.project_tree.item(selection[0])['text']

        # 确认删除
        result = messagebox.askyesno(
            "确认删除",
            f"确定要删除项目 '{project_name}' 吗？\n\n这将删除所有相关文件（Markdown、图片、缓存），但会保留Anki文件。"
        )

        if result:
            try:
                success = self.processor.file_manager.cleanup_project(project_name, keep_anki=True)
                if success:
                    messagebox.showinfo("删除成功", f"项目 '{project_name}' 已删除")
                    self.refresh_project_list()
                else:
                    messagebox.showerror("删除失败", f"删除项目 '{project_name}' 失败")

            except Exception as e:
                self.log_message(f"删除项目失败: {e}", level="ERROR")
                messagebox.showerror("错误", f"删除项目失败: {e}")

    # ==================== UI控制方法 ====================

    def show_progress(self):
        """显示进度条"""
        self.main_progress.pack(side="right", padx=(5, 2), pady=2)
        self.main_progress.start()

    def hide_progress(self):
        """隐藏进度条"""
        self.main_progress.stop()
        self.main_progress.pack_forget()

    def disable_processing_buttons(self):
        """禁用处理按钮"""
        self.process_single_btn.config(state="disabled")
        self.process_batch_btn.config(state="disabled")
        self.md_to_anki_btn.config(state="disabled")

    def enable_processing_buttons(self):
        """启用处理按钮"""
        self.process_single_btn.config(state="normal")
        self.process_batch_btn.config(state="normal")
        self.md_to_anki_btn.config(state="normal")

    def toggle_log_display(self):
        """切换日志显示"""
        if self.log_visible:
            self.log_frame.pack_forget()
            self.toggle_log_btn.config(text="显示日志")
            self.log_visible = False
        else:
            self.log_frame.pack(fill="both", expand=True, padx=5, pady=(0, 5))
            self.toggle_log_btn.config(text="隐藏日志")
            self.log_visible = True

    def log_message(self, message: str, level: str = "INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # 限制日志长度
        lines = int(self.log_text.index('end-1c').split('.')[0])
        if lines > 1000:
            self.log_text.delete(1.0, "100.0")

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清空")

    def save_log(self):
        """保存日志"""
        filename = filedialog.asksaveasfilename(
            title="保存日志文件",
            defaultextension=".log",
            filetypes=[("日志文件", "*.log"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("保存成功", f"日志已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("保存失败", f"保存日志失败: {e}")

    # ==================== 设置方法 ====================

    def save_settings(self):
        """保存设置"""
        # 这里可以实现设置保存功能
        messagebox.showinfo("设置", "设置保存功能待实现")

    def reset_settings(self):
        """重置设置"""
        # 重置为默认值
        self.api_url_var.set("")
        self.api_key_var.set("")
        self._actual_api_key = ""
        self.model_name_var.set("")
        self.pdf_top_margin_var.set("50.0")
        self.pdf_bottom_margin_var.set("50.0")
        self.max_workers_var.set("10")
        self.chunk_size_var.set("500")
        self.chunk_stride_var.set("450")
        self.lines_to_skip_var.set("13")
        messagebox.showinfo("设置", "设置已重置为默认值")

    # ==================== 菜单方法 ====================

    def clear_cache(self):
        """清理缓存"""
        result = messagebox.askyesno("确认清理", "确定要清理所有缓存文件吗？")
        if result:
            try:
                cache_dir = Path("documents/cache")
                if cache_dir.exists():
                    import shutil
                    shutil.rmtree(cache_dir)
                    cache_dir.mkdir(exist_ok=True)
                messagebox.showinfo("清理完成", "缓存文件已清理")
                self.log_message("缓存文件已清理")
            except Exception as e:
                messagebox.showerror("清理失败", f"清理缓存失败: {e}")

    def export_project_list(self):
        """导出项目列表"""
        filename = filedialog.asksaveasfilename(
            title="导出项目列表",
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                projects = self.processor.file_manager.list_projects()

                with open(filename, 'w', encoding='utf-8', newline='') as f:
                    import csv
                    writer = csv.writer(f)
                    writer.writerow(["项目名称", "Markdown文件", "图片数量", "已缓存", "已生成Anki"])

                    for project in projects:
                        writer.writerow([
                            project['name'],
                            project['markdown_file'],
                            project['image_count'],
                            "是" if project['has_cache'] else "否",
                            "是" if project['has_anki'] else "否"
                        ])

                messagebox.showinfo("导出成功", f"项目列表已导出到: {filename}")

            except Exception as e:
                messagebox.showerror("导出失败", f"导出项目列表失败: {e}")

    def show_help(self):
        """显示帮助信息"""
        help_text = """
文档处理器 GUI 使用说明

1. 单文档处理：
   - 选择要处理的文档文件（支持PDF、Word、Markdown、文本）
   - 选择处理选项（提取为Markdown、同时生成Anki）
   - 点击"开始处理"

2. 批量处理：
   - 选择包含文档的目录
   - 设置处理选项（递归处理、跳过已处理）
   - 点击"开始批量处理"

3. 项目管理：
   - 查看所有已处理的项目
   - 搜索和过滤项目
   - 查看项目详情、打开文件夹、删除项目

4. Anki生成：
   - 输入项目名称（支持模糊匹配）
   - 选择交互模式
   - 或直接选择Markdown文件转换

5. 设置：
   - 配置AI服务参数
   - 调整处理参数

支持的文档格式：PDF、Word(.docx/.doc)、Markdown、文本文件
        """

        dialog = tk.Toplevel(self.root)
        dialog.title("使用说明")
        dialog.geometry("600x500")
        dialog.transient(self.root)

        text_widget = scrolledtext.ScrolledText(dialog, wrap=tk.WORD)
        text_widget.pack(fill="both", expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, help_text)
        text_widget.config(state="disabled")

        ttk.Button(dialog, text="关闭", command=dialog.destroy).pack(pady=10)

    def show_about(self):
        """显示关于信息"""
        about_text = """
文档处理器 GUI v2.2

一个强大的文档处理工具，支持多种文档格式的提取和转换。

主要功能：
• 多格式文档处理（PDF、Word、Markdown、文本）
• 智能图片提取和定位
• AI驱动的Anki闪卡生成
• 项目化管理
• 批量处理

开发者：Document Processor Team
版本：v2.2
更新日期：2025-06-15
        """

        messagebox.showinfo("关于", about_text)

    def on_closing(self):
        """窗口关闭事件"""
        if self.processing:
            result = messagebox.askyesno("确认退出", "正在处理中，确定要退出吗？")
            if not result:
                return

        self.root.destroy()

    # ==================== 新增功能方法 ====================

    def setup_drag_and_drop(self):
        """设置拖拽功能"""
        try:
            # 尝试导入tkinterdnd2
            from tkinterdnd2 import DND_FILES

            # 为文件输入框设置拖拽（延迟到组件创建后）
            self.root.after(100, self._setup_drag_targets)

            self.log_message("拖拽功能已启用")

        except ImportError:
            self.log_message("拖拽功能不可用，可选安装: pip install tkinterdnd2", level="INFO")
        except Exception as e:
            self.log_message(f"拖拽功能设置失败: {e}", level="WARNING")

    def _setup_drag_targets(self):
        """设置拖拽目标（延迟执行）"""
        try:
            from tkinterdnd2 import DND_FILES

            # 为文件输入框设置拖拽
            if hasattr(self, 'file_entry'):
                self.file_entry.drop_target_register(DND_FILES)
                self.file_entry.dnd_bind('<<Drop>>', self.on_file_drop)

            if hasattr(self, 'dir_entry'):
                self.dir_entry.drop_target_register(DND_FILES)
                self.dir_entry.dnd_bind('<<Drop>>', self.on_dir_drop)

            if hasattr(self, 'md_file_entry'):
                self.md_file_entry.drop_target_register(DND_FILES)
                self.md_file_entry.dnd_bind('<<Drop>>', self.on_md_file_drop)

        except Exception as e:
            self.log_message(f"设置拖拽目标失败: {e}", level="WARNING")

    def on_file_drop(self, event):
        """处理文件拖拽"""
        files = self.root.tk.splitlist(event.data)
        if files:
            file_path = files[0]
            if os.path.isfile(file_path):
                self.selected_file_var.set(file_path)
                self.log_message(f"拖拽文件: {file_path}")
                # 自动切换到单文档处理标签页
                self.notebook.select(0)

    def on_dir_drop(self, event):
        """处理目录拖拽"""
        files = self.root.tk.splitlist(event.data)
        if files:
            dir_path = files[0]
            if os.path.isdir(dir_path):
                self.selected_dir_var.set(dir_path)
                self.log_message(f"拖拽目录: {dir_path}")
                # 自动切换到批量处理标签页
                self.notebook.select(1)

    def on_md_file_drop(self, event):
        """处理Markdown文件拖拽"""
        files = self.root.tk.splitlist(event.data)
        if files:
            file_path = files[0]
            if os.path.isfile(file_path) and file_path.lower().endswith('.md'):
                self.md_file_var.set(file_path)
                self.log_message(f"拖拽Markdown文件: {file_path}")
                # 自动切换到Markdown转Anki标签页
                self.notebook.select(3)

    def generate_anki_for_selected_project(self):
        """为选中的项目生成Anki闪卡"""
        selection = self.project_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个项目")
            return

        if self.processing:
            messagebox.showwarning("处理中", "已有处理任务在进行中，请等待完成")
            return

        project_name = self.project_tree.item(selection[0])['text']

        # 确认生成
        result = messagebox.askyesno(
            "确认生成",
            f"确定要为项目 '{project_name}' 生成Anki闪卡吗？"
        )

        if result:
            # 启动处理线程
            self.start_processing("generate_anki", {
                'project_name': project_name,
                'interactive': False  # 项目管理中直接生成，不需要交互
            })

    def toggle_api_key_visibility(self):
        """切换API密钥显示/隐藏"""
        if self.api_key_show_var.get():
            # 当前是显示状态，切换为隐藏
            self.api_key_entry.config(show="*")
            self.show_key_btn.config(text="显示")
            self.api_key_show_var.set(False)
            # 如果当前显示的是真实密钥，改为星号
            if hasattr(self, '_actual_api_key') and self.api_key_var.get() == self._actual_api_key:
                self.api_key_var.set('*' * len(self._actual_api_key))
        else:
            # 当前是隐藏状态，切换为显示
            self.api_key_entry.config(show="")
            self.show_key_btn.config(text="隐藏")
            self.api_key_show_var.set(True)
            # 显示真实密钥
            if hasattr(self, '_actual_api_key'):
                self.api_key_var.set(self._actual_api_key)

    def load_settings_from_config(self):
        """从config.ini加载设置"""
        config_path = "config.ini"
        if not os.path.exists(config_path):
            self.log_message("配置文件不存在，使用默认设置")
            return

        try:
            import configparser
            parser = configparser.ConfigParser()
            parser.read(config_path, encoding="utf-8")

            if "DEFAULT" in parser:
                cfg = parser["DEFAULT"]

                # 加载AI设置
                self.api_url_var.set(cfg.get('API_BASE_URL', ''))

                # API密钥用*号显示
                api_key = cfg.get('API_KEY', '')
                if api_key:
                    self.api_key_var.set('*' * len(api_key))  # 显示为星号
                    self._actual_api_key = api_key  # 保存实际密钥
                else:
                    self.api_key_var.set('')
                    self._actual_api_key = ''

                self.model_name_var.set(cfg.get('MODEL_NAME', ''))

                # 加载处理设置
                self.pdf_top_margin_var.set(cfg.get('PDF_TOP_MARGIN', '50.0'))
                self.pdf_bottom_margin_var.set(cfg.get('PDF_BOTTOM_MARGIN', '50.0'))
                self.max_workers_var.set(cfg.get('MAX_WORKERS', '10'))
                self.chunk_size_var.set(cfg.get('CHUNK_SIZE', '500'))
                self.chunk_stride_var.set(cfg.get('CHUNK_STRIDE', '450'))
                self.lines_to_skip_var.set(cfg.get('LINES_TO_SKIP', '13'))

                self.log_message("设置已从配置文件加载")

        except Exception as e:
            self.log_message(f"加载配置文件失败: {e}", level="ERROR")

    def run(self):
        """运行GUI应用程序"""
        # 初始化时刷新项目列表
        self.refresh_project_list()

        # 启动主循环
        self.root.mainloop()


def main():
    """主函数"""
    try:
        # 尝试使用支持拖拽的tkinter
        from tkinterdnd2 import TkinterDnD

        # 创建支持拖拽的GUI应用程序
        class DragDropGUI(DocumentProcessorGUI):
            def __init__(self):
                # 创建支持拖拽的根窗口
                self.root = TkinterDnD.Tk()
                self.root.title("文档处理器 - Document Processor GUI")
                self.root.geometry("1000x700")
                self.root.minsize(800, 600)

                # 初始化其他组件
                self.processor = None
                self.message_queue = __import__('queue').Queue()
                self.processing = False
                self.current_operation = None

                # 创建GUI组件
                self.create_widgets()
                self.setup_layout()
                self.setup_menu()

                # 初始化后端处理器
                self.init_processor()

                # 启动消息处理循环
                self.process_queue()

                # 设置拖拽功能
                self.setup_drag_and_drop()

                # 加载设置
                self.load_settings_from_config()

                # 设置窗口关闭事件
                self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 创建并运行拖拽版GUI
        app = DragDropGUI()
        app.run()

    except ImportError:
        # 如果没有tkinterdnd2，使用普通版本
        print("提示：安装 tkinterdnd2 可启用拖拽功能: pip install tkinterdnd2")
        app = DocumentProcessorGUI()
        app.run()

    except Exception as e:
        print(f"启动拖拽版本失败: {e}")
        print("使用普通版本...")
        app = DocumentProcessorGUI()
        app.run()


if __name__ == "__main__":
    main()
