# 文档处理器 GUI 使用指南

## 🚀 快速开始

### 安装依赖
```bash
# 安装所有依赖（包括可选的拖拽功能）
pip install -r requirements.txt
```

### 启动应用程序
```bash
python document_processor_gui.py
```

### 功能说明
- **自动检测拖拽支持**：如果安装了tkinterdnd2，自动启用拖拽功能
- **向下兼容**：没有拖拽库时仍可正常使用所有核心功能
- **统一界面**：单一GUI文件，包含所有功能

## 📋 功能概览

### 🖥️ 界面布局
GUI应用程序采用标签页设计，包含以下5个主要功能模块：

1. **单文档处理** - 处理单个文档文件
2. **批量处理** - 批量处理目录中的多个文档
3. **项目管理** - 管理和查看已处理的项目
4. **Markdown转Anki** - 直接将Markdown文件转换为Anki闪卡
5. **设置** - 配置AI服务和处理参数

## 📄 单文档处理

### 基本操作
1. 点击"单文档处理"标签页
2. 点击"浏览..."按钮选择文档文件
3. 选择处理选项：
   - ✅ 提取为Markdown（默认选中）
   - ⭕ 同时生成Anki闪卡（可选）
4. 点击"开始处理"

### 支持的文档格式
- **PDF文件** (.pdf)
- **Word文档** (.docx, .doc)
- **Markdown文件** (.md)
- **文本文件** (.txt)

### 拖拽功能
- 直接将文件拖拽到文件路径输入框
- 自动识别文件类型并切换到相应标签页

## 📁 批量处理

### 批量处理步骤
1. 点击"批量处理"标签页
2. 选择包含文档的目录
3. 配置批量处理选项：
   - **递归处理子目录**：处理子文件夹中的文档
   - **跳过已处理的文件**：避免重复处理
4. 点击"开始批量处理"

### 进度跟踪
- 实时进度条显示处理进度
- 状态栏显示当前处理的文件
- 结果区域显示详细的处理统计

## 🗂️ 项目管理

### 项目列表功能
- **查看所有项目**：显示项目名称、类型、图片数量、状态
- **搜索过滤**：实时搜索项目名称
- **项目状态**：显示是否包含图片、缓存、Anki文件

### 项目操作
- **查看详情**：显示项目的详细信息
- **打开文件夹**：在文件管理器中打开项目目录
- **生成Anki闪卡**：直接为选中项目生成Anki闪卡
- **删除项目**：删除项目文件（保留Anki文件）

### 快速Anki生成
1. 在项目列表中选择项目
2. 点击"生成Anki闪卡"按钮
3. 确认生成操作
4. 查看生成结果

## 🎴 Markdown转Anki

### 直接转换功能
1. 点击"Markdown转Anki"标签页
2. 选择要转换的Markdown文件
3. 配置转换选项：
   - **保留图片引用**：保持图片链接
4. 点击"开始转换"

### 适用场景
- 已有的Markdown笔记文件
- 从其他来源获得的Markdown文档
- 不需要完整项目管理的快速转换

## ⚙️ 设置管理

### AI服务配置
- **API Base URL**：AI服务的API地址
- **API Key**：API密钥（支持显示/隐藏）
- **模型名称**：使用的AI模型

### 处理参数
- **PDF顶部边距**：PDF处理时的顶部边距
- **PDF底部边距**：PDF处理时的底部边距
- **最大并发数**：并行处理的最大线程数

### 设置操作
- **保存设置**：保存当前配置
- **重置默认**：恢复默认设置
- **自动加载**：启动时自动从config.ini加载设置

## 🔧 高级功能

### 后台处理
- 所有处理操作在后台线程中执行
- 界面保持响应，不会卡顿
- 实时显示处理进度和状态

### 错误处理
- 用户友好的错误消息
- 详细的错误日志记录
- 优雅的异常处理

### 日志系统
- 可折叠的日志显示区域
- 支持保存日志到文件
- 清空日志功能
- 自动限制日志长度

## 🎯 使用技巧

### 高效工作流程
1. **首次使用**：在设置页面配置AI服务参数
2. **单文档处理**：拖拽文件到界面，选择处理选项
3. **批量处理**：选择文档目录，启用递归处理
4. **项目管理**：使用搜索功能快速找到项目
5. **Anki生成**：在项目管理中直接生成，或使用独立转换

### 快捷操作
- **拖拽文件**：直接拖拽到相应输入框
- **双击选择**：在项目选择对话框中双击项目
- **搜索过滤**：实时搜索项目，无需按回车
- **状态显示**：查看进度条和状态栏了解处理进度

### 故障排除
- **拖拽不工作**：运行 `python install_gui_dependencies.py` 安装拖拽支持
- **AI服务错误**：检查设置页面的API配置
- **处理失败**：查看日志区域的详细错误信息
- **界面卡顿**：等待后台处理完成，或重启应用程序

## 📞 获取帮助

### 内置帮助
- 菜单栏 → 帮助 → 使用说明
- 菜单栏 → 帮助 → 关于

### 日志查看
- 点击状态栏的"显示日志"按钮
- 查看详细的操作日志和错误信息
- 保存日志文件用于问题诊断

---

**提示**：首次使用建议先在设置页面配置AI服务参数，然后从单文档处理开始熟悉界面操作。
