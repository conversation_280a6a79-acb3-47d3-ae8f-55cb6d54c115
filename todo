1.图片使用<img alt="undefined" src="1920px-Wye-delta.svg">这样子进行包裹，然后需要注意不同的pdf、word避免重合。，所以要有前缀



优化pdf2md中的图片的位置：
无论是图片还是文本，在PDF页面上都有一个“边界框”（Bounding Box），它由四个坐标值定义，通常是 (x0, y0, x1, y1)，分别代表框左下角和右上角的坐标。

(x0, y0): 边界框左下角的坐标。
(x1, y1): 边界框右上角的坐标。
您的任务流程如下：

遍历PDF的每一页。
获取页面上所有图片及其边界框。
获取页面上所有文本块及其边界框。
对于每个图片，通过计算其边界框与周围文本块边界框的距离，来判断它是在某段文字的上方、下方还是中间。



提示词优化，按照题目的范围使用图片，按照答案的范围使用图片。因为有的答案也会使用图片。


降低图片比对时间，使用更好用的算法。


以chunk发送给ai，但发送给ai的chunk中没有 “第x页” 这类。（处理时就不加入）


todo，如果文本处理ai表明不能够妥善处理图片问题，则返回给视觉ai，让视觉ai提供更多信息，往复操作。


优化  python document_processor.py --anki 输入允许部分输入，类似于git的分支前五个字符。