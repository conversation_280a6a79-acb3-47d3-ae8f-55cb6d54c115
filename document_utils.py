#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
document_utils.py

文档处理工具模块
提供文档名称清理、图像文件夹命名等实用功能
"""

import re
import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple


class DocumentNameCleaner:
    """文档名称清理器"""
    
    # Windows 文件系统不允许的字符
    WINDOWS_INVALID_CHARS = r'[<>:"/\\|?*]'
    
    # 其他可能有问题的字符
    PROBLEMATIC_CHARS = {
        '【': '[',
        '】': ']',
        '（': '(',
        '）': ')',
        '，': ',',
        '。': '.',
        '：': '_',
        '；': ';',
        '！': '!',
        '？': '?',
        '"': '"',
        '"': '"',
        ''': "'",
        ''': "'",
    }
    
    def __init__(self):
        """初始化文档名称清理器"""
        pass
    
    def clean_document_name(self, name: str, max_length: int = 200) -> str:
        """
        清理文档名称，移除或替换有问题的字符
        
        Args:
            name: 原始文档名称
            max_length: 最大长度限制
            
        Returns:
            清理后的文档名称
        """
        if not name:
            return "unnamed_document"
        
        # 移除文件扩展名（如果有）
        name_without_ext = Path(name).stem
        
        # 替换中文标点符号
        cleaned_name = name_without_ext
        for chinese_char, english_char in self.PROBLEMATIC_CHARS.items():
            cleaned_name = cleaned_name.replace(chinese_char, english_char)
        
        # 移除 Windows 不允许的字符
        cleaned_name = re.sub(self.WINDOWS_INVALID_CHARS, '_', cleaned_name)
        
        # 移除多余的空格和下划线
        cleaned_name = re.sub(r'\s+', '_', cleaned_name)  # 空格替换为下划线
        cleaned_name = re.sub(r'_+', '_', cleaned_name)   # 多个下划线合并为一个
        cleaned_name = cleaned_name.strip('_')            # 移除首尾下划线
        
        # 限制长度
        if len(cleaned_name) > max_length:
            cleaned_name = cleaned_name[:max_length].rstrip('_')
        
        # 确保不为空
        if not cleaned_name:
            cleaned_name = "unnamed_document"
        
        return cleaned_name
    
    def generate_safe_folder_name(self, document_name: str, suffix: str = "") -> str:
        """
        生成安全的文件夹名称
        
        Args:
            document_name: 文档名称
            suffix: 可选后缀
            
        Returns:
            安全的文件夹名称
        """
        clean_name = self.clean_document_name(document_name)
        
        if suffix:
            clean_suffix = self.clean_document_name(suffix)
            return f"{clean_name}_{clean_suffix}"
        
        return clean_name


class ImageFolderManager:
    """图像文件夹管理器"""
    
    def __init__(self, name_cleaner: DocumentNameCleaner = None):
        """
        初始化图像文件夹管理器
        
        Args:
            name_cleaner: 文档名称清理器实例
        """
        self.name_cleaner = name_cleaner or DocumentNameCleaner()
    
    def generate_image_folder_name(self, document_name: str, strategy: str = "suffix") -> str:
        """
        生成图像文件夹名称
        
        Args:
            document_name: 文档名称
            strategy: 命名策略 ("suffix" 或 "prefix")
                     - "suffix": images_documentname
                     - "prefix": documentname_images
                     
        Returns:
            图像文件夹名称
        """
        clean_doc_name = self.name_cleaner.clean_document_name(document_name)
        
        if strategy == "prefix":
            return f"{clean_doc_name}_images"
        else:  # default to suffix
            return f"images_{clean_doc_name}"
    
    def rename_image_folder(self, extraction_dir: Path, document_name: str, 
                          strategy: str = "suffix") -> Tuple[bool, str]:
        """
        重命名提取目录中的图像文件夹
        
        Args:
            extraction_dir: 提取目录路径
            document_name: 文档名称
            strategy: 命名策略
            
        Returns:
            (是否成功, 新文件夹路径或错误信息)
        """
        old_images_dir = extraction_dir / "images"
        
        if not old_images_dir.exists():
            return True, "没有images文件夹需要重命名"
        
        new_folder_name = self.generate_image_folder_name(document_name, strategy)
        new_images_dir = extraction_dir / new_folder_name
        
        try:
            # 如果目标文件夹已存在，先删除
            if new_images_dir.exists():
                import shutil
                shutil.rmtree(new_images_dir)
            
            # 重命名文件夹
            old_images_dir.rename(new_images_dir)
            
            logging.info(f"图像文件夹重命名成功: {old_images_dir} -> {new_images_dir}")
            return True, str(new_images_dir)
            
        except Exception as e:
            error_msg = f"重命名图像文件夹失败: {e}"
            logging.error(error_msg)
            return False, error_msg


class MarkdownImagePathUpdater:
    """Markdown图像路径更新器"""
    
    def __init__(self, name_cleaner: DocumentNameCleaner = None):
        """
        初始化Markdown图像路径更新器
        
        Args:
            name_cleaner: 文档名称清理器实例
        """
        self.name_cleaner = name_cleaner or DocumentNameCleaner()
    
    def update_image_paths_in_markdown(self, markdown_content: str, 
                                     old_folder_name: str, 
                                     new_folder_name: str) -> str:
        """
        更新Markdown内容中的图像路径
        
        Args:
            markdown_content: 原始Markdown内容
            old_folder_name: 旧的图像文件夹名称
            new_folder_name: 新的图像文件夹名称
            
        Returns:
            更新后的Markdown内容
        """
        # 匹配Markdown图片格式: ![alt](path)
        img_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
        
        def replace_image_path(match):
            alt_text = match.group(1)
            original_path = match.group(2)
            
            # 如果路径包含旧的文件夹名称，则替换
            if old_folder_name in original_path:
                new_path = original_path.replace(old_folder_name, new_folder_name)
                return f"![{alt_text}]({new_path})"
            
            return match.group(0)  # 不变
        
        updated_content = re.sub(img_pattern, replace_image_path, markdown_content)
        return updated_content
    
    def update_image_paths_for_anki(self, markdown_content: str, 
                                  project_name: str,
                                  image_folder_name: str) -> str:
        """
        为Anki格式更新Markdown内容中的图像路径
        
        Args:
            markdown_content: 原始Markdown内容
            project_name: 项目名称
            image_folder_name: 图像文件夹名称
            
        Returns:
            更新后的Markdown内容
        """
        # 匹配Markdown图片格式: ![alt](path)
        img_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
        
        def replace_image_path(match):
            alt_text = match.group(1)
            original_path = match.group(2)
            
            # 提取文件名
            img_filename = Path(original_path).name
            
            # 生成新的Anki格式路径: project_name/filename
            new_path = f"{project_name}/{img_filename}"
            
            return f"![{alt_text}]({new_path})"
        
        updated_content = re.sub(img_pattern, replace_image_path, markdown_content)
        return updated_content


def create_document_utils() -> Tuple[DocumentNameCleaner, ImageFolderManager, MarkdownImagePathUpdater]:
    """
    创建文档处理工具实例
    
    Returns:
        (文档名称清理器, 图像文件夹管理器, Markdown路径更新器)
    """
    name_cleaner = DocumentNameCleaner()
    image_manager = ImageFolderManager(name_cleaner)
    path_updater = MarkdownImagePathUpdater(name_cleaner)
    
    return name_cleaner, image_manager, path_updater
