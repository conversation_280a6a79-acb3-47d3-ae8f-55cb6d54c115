## Multi-Document Question Bank Generation System

> Complete multi-document processing pipeline: PDF/Word → Markdown → Anki flashcards. Extract text/images from various document formats, then intelligently convert structured question-answer content to Anki-compatible format using AI.

> Enhanced system supporting PDF, Word documents with unified file management, project-based organization, and intelligent naming conventions. Features batch processing, advanced image handling, and smart cross-chunk validation.

> Completed - v2.2 Enhanced Document Processing System with improved file organization and Windows compatibility

> Solo Developer

> Python 3.x, PyMuPDF, python-docx, OpenAI-compatible APIs, Concurrent Processing, Streaming API, Modular Architecture, Project Management

## Dependencies

### Core Dependencies
* PyMuPDF (>=1.23.0): PDF parsing and image extraction
* requests (>=2.25.0): HTTP client for AI API calls
* configparser (>=5.0.0): Configuration management
* concurrent.futures: Parallel processing
* tqdm (>=4.60.0): Progress tracking
* pathlib (>=1.0.0): Modern file path handling

### Optional Dependencies
* python-docx (>=0.8.11): Word document processing (.docx/.doc support)
* Pillow (>=9.0.0): Enhanced image processing and format conversion
* opencv-python (>=4.5.0): Advanced image similarity detection (optional)
* numpy (>=1.21.0): Numerical operations for image processing

### Utility Dependencies
* csv: CSV file handling for validation reports
* json: JSON data processing and caching
* re: Regular expressions for pattern matching
* zipfile: Word document internal structure access
* xml.etree.ElementTree: XML parsing for document structure

## Development Environment

> Python 3.x (3.8+ recommended)
> pip package manager
> AI API endpoint (OpenAI-compatible)
> Streaming API support
> Optional: Microsoft Word (for advanced Word document features)

## Supported Document Formats

| Format | Extensions | Text Extraction | Image Extraction | Table Processing | Anki Generation | Status |
|--------|------------|-----------------|------------------|------------------|-----------------|--------|
| PDF | .pdf | ✅ | ✅ | - | ✅ | Full Support |
| Word | .docx, .doc | ✅ | ✅ | ✅ | ✅ | Full Support |
| Markdown | .md | ✅ | ✅ | - | ✅ | Full Support |
| Text | .txt | ✅ | - | - | ✅ | Basic Support |

## Structure

```
root
# Core Multi-Document Processing System (v2.2)
- document_processor.py           // Unified multi-document processor with integrated Anki generation
- file_manager.py                 // Unified file naming and project management system
- pdf_extractor.py                // Enhanced PDF document processor with smart image positioning
- word_extractor.py               // Word document processor (.docx/.doc support)
- content_filter.py               // Advanced content filtering with image similarity detection
- demo_multi_document.py          // Comprehensive system demonstration script

# Document Processing Utilities (NEW v2.2)
- document_utils.py               // Document name cleaning and image folder management utilities
- project_migration.py           // Project migration tool for updating existing projects
- fix_image_paths.py             // Image path correction tool for Markdown files

# Content Filtering System
- filter_config.ini               // Content filtering configuration
- trash_image/                    // Spam/unwanted image templates
  - weChat.png                    // Sample spam image template
- filtered_images/                // Detected spam images with similarity scores

# Document Processing Workspace
- documents/                      // New project-based workspace
  - source/                       // Original document storage
  - extracted/                    // Processed content by project
    - [project_name]/
      - [document].md             // Extracted Markdown content
      - images_[document_name]/   // Project-specific images with document name (NEW v2.2)
  - anki/                         // Generated Anki flashcards
  - cache/                        // Project-specific processing cache
    - [project_name]/
  - logs/                         // Project-specific processing logs

# Legacy System (Fully Compatible)
- tiku.pdf                        // Original PDF source file
- extract_pdf_md.py               // Legacy PDF extraction script
- md_to_anki.py                   // Main script: Markdown → Anki flashcards
- ai_service.py                   // Unified AI service module with streaming support
- anki_generator.py               // Modular Anki card generation utilities
- tiku_md/                        // Legacy extracted content directory
  - tiku.md                       // Original extracted Markdown content
  - images/                       // Legacy image storage
- cache/                          // Legacy cache system
  - *.json                        // Individual chunk results
  - *.json.backup                 // Backup files for regenerated chunks

# Configuration and Tools
- config.ini                      // Enhanced configuration (multi-document + legacy)
- filter_config.ini               // Content filtering configuration with similarity thresholds
- requirements.txt                // Updated Python dependencies
- README_v2.md                    // Comprehensive system documentation
- USAGE_GUIDE.md                  // Detailed usage instructions
- test_*.py                       // Testing scripts for validation
- test_similarity_threshold.py    // Image similarity threshold testing tool
- analyze_filtered_images.py      // Analysis tool for filtered image distribution
- check_missing_images.py         // Cross-chunk image validation tool
- regenerate_missing_chunks.py    // Selective chunk regeneration tool

# Output Files
- ankiflashcards.txt              // Final Anki import file (TSV format)
- problematic_chunks.csv          // Validation report for missing images
- *.log                           // Various processing logs
```

## File Naming Conventions

### Project Naming
- Format: `{cleaned_filename}_{timestamp}`
- Example: `textbook_20240614_143052`
- Chinese: `我的笔记_20240614_143052`

### Image Naming
- Format: `{project_name}_{source_type}_{page_number:03d}_img_{index:03d}.png`
- Example: `textbook_20240614_143052_page_001_img_001.png`
- Word: `notes_20240614_143052_doc_000_img_001.png`
- Enhanced: File-specific prefixes like `【冲刺版】2024北sen整理题库数学百题_page_4_img_1.png`

### Image Folder Naming (NEW v2.2)
- Format: `images_{cleaned_document_name}`
- Example: `images_冲刺版_2024秋招北森最新整理题库数学百题_可搜_20250615_123819`
- Windows Compatible: Special characters cleaned for file system compatibility
- Document Name Cleaning: Removes problematic characters like spaces, colons, brackets

### Text Formatting in Markdown
- Color Support: `<span style="color:rgb(255, 0, 0)">红色文本</span>`
- Bold Text: `**粗体文本**`
- Combined: `<span style="color:rgb(255, 0, 0)">**红色粗体**</span>`
- Word Format Preservation: Maintains original document formatting in Markdown output

### Image Integration
- Inline Placement: Images appear exactly where they exist in the original document
- HTML Tags: `<img alt="undefined" src="images/filename.png">`
- Precise Positioning: No duplicate images in "其他图片" sections
- Context Preservation: Images maintain relationship with surrounding text

## Advanced Image Processing Features

### Smart Image Positioning (PDF)
- Bounding Box Analysis: Extract precise coordinates for all text and image elements
- Spatial Relationship Detection: Analyze proximity between images and text blocks
- Intelligent Insertion: Insert images based on spatial relationships (above/below/between paragraphs)
- Position Types: `before_paragraph`, `after_paragraph`, `between_paragraphs`, `inline`, `standalone`

### Content Filtering System
- Spam Image Detection: Identify and filter unwanted advertising images
- Similarity Analysis: Advanced image similarity comparison using PIL and optional OpenCV
- Configurable Thresholds: Adjustable similarity thresholds (0.0-1.0)
- Filtered Image Archive: Save detected spam images with similarity scores
- Text Content Filtering: Block specified advertising text patterns

### Similarity Detection Methods
- Histogram Comparison: Color distribution analysis for basic similarity
- Perceptual Hashing: Structure-based comparison for layout similarity
- Template Matching: Direct comparison with known spam templates
- Configurable Algorithms: Support for multiple similarity calculation methods

## Content Filter Configuration

### Filter Settings (filter_config.ini)
```ini
[CONTENT_FILTER]
similarity_threshold = 0.7              # Image similarity threshold (0.0-1.0)
trash_image_dir = trash_image           # Spam image templates directory
filtered_images_dir = filtered_images   # Filtered images archive
blocked_texts = 专业笔试助攻,代做:jobhelp101,jobhelp101  # Blocked text patterns

[LOGGING]
verbose_filtering = true                # Enable detailed filtering logs
```

### Filtered Image Naming
- Format: `filtered_page{页码}_img{图片索引}_sim{相似度}_template_{模板名}.png`
- Example: `filtered_page15_img2_sim0.987_template_weChat.png`
- Similarity Range: 0.000-1.000 (higher values indicate greater similarity)

### Threshold Recommendations
- **0.95-1.0**: Only filter nearly identical images (most conservative)
- **0.9-0.95**: Filter very similar images (recommended for production)
- **0.85-0.9**: Filter highly similar images (balanced approach)
- **0.8-0.85**: Filter moderately similar images
- **0.7-0.8**: Filter loosely similar images (may cause false positives)
- **<0.7**: Not recommended (high false positive rate)

## Testing and Analysis Tools

### Similarity Threshold Testing
- `test_similarity_threshold.py`: Test different threshold values
- `analyze_filtered_images.py`: Analyze distribution of filtered images
- Manual Review: Examine filtered images to adjust thresholds
- Performance Metrics: Track false positives and missed detections

## Recent Updates (v2.2)

### Document Processing Improvements
- **Fixed Extraction Folder Detection**: Corrected path detection logic in `_copy_images_for_anki` method
- **Enhanced Image Folder Naming**: Images now stored in `images_{document_name}` folders for better organization
- **Document Name Cleaning**: Implemented Windows-compatible file name cleaning with special character handling
- **Markdown Path Updates**: Automatic image path updates to maintain consistency after folder renaming

### New Utility Modules
- **document_utils.py**: Comprehensive document processing utilities
  - `DocumentNameCleaner`: Handles Windows file system compatibility
  - `ImageFolderManager`: Manages image folder naming and renaming
  - `MarkdownImagePathUpdater`: Updates image references in Markdown files
- **project_migration.py**: Tool for migrating existing projects to new naming conventions
- **fix_image_paths.py**: Standalone tool for correcting image paths in Markdown files

### Bug Fixes
- **Anki Generation**: Fixed "项目没有提取的文件夹" error by correcting folder detection logic
- **Image Path Resolution**: Resolved image path mismatches between Markdown files and actual folder structure
- **Windows Compatibility**: Enhanced file naming to prevent Windows file system issues

### Migration Support
- **Backward Compatibility**: Existing projects continue to work without modification
- **Automatic Migration**: Tools provided for updating existing projects to new structure
- **Path Validation**: Enhanced validation to ensure image paths remain functional after migration
