#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
markdown_detector.py

智能Markdown文件检测和选择工具
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from difflib import SequenceMatcher
import logging


class MarkdownDetector:
    """智能Markdown文件检测器"""
    
    def __init__(self):
        """初始化Markdown检测器"""
        pass
    
    def find_markdown_files(self, directory: str) -> List[Dict[str, str]]:
        """
        在指定目录中查找所有markdown文件
        
        Args:
            directory: 目录路径
            
        Returns:
            markdown文件信息列表
        """
        dir_path = Path(directory)
        if not dir_path.exists() or not dir_path.is_dir():
            return []
        
        md_files = []
        
        # 查找所有.md文件
        for md_file in dir_path.glob('*.md'):
            if md_file.is_file():
                file_info = self._analyze_markdown_file(md_file)
                md_files.append(file_info)
        
        # 按文件大小和修改时间排序（大文件和新文件优先）
        md_files.sort(key=lambda x: (x['size'], x['mtime']), reverse=True)
        return md_files
    
    def _analyze_markdown_file(self, file_path: Path) -> Dict[str, str]:
        """
        分析markdown文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        stat = file_path.stat()
        
        # 尝试读取文件前几行来判断内容类型
        content_preview = ""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = []
                for i, line in enumerate(f):
                    if i >= 10:  # 只读前10行
                        break
                    lines.append(line.strip())
                content_preview = '\n'.join(lines)
        except Exception:
            pass
        
        return {
            'path': str(file_path),
            'name': file_path.name,
            'stem': file_path.stem,
            'size': stat.st_size,
            'mtime': stat.st_mtime,
            'content_preview': content_preview,
            'is_likely_main': self._is_likely_main_file(file_path, content_preview)
        }
    
    def _is_likely_main_file(self, file_path: Path, content_preview: str) -> bool:
        """
        判断是否可能是主要的markdown文件
        
        Args:
            file_path: 文件路径
            content_preview: 内容预览
            
        Returns:
            是否可能是主文件
        """
        name_lower = file_path.stem.lower()
        
        # 排除一些明显的辅助文件
        exclude_patterns = [
            'readme', 'index', 'toc', 'summary', 'temp', 'test', 'draft'
        ]
        
        for pattern in exclude_patterns:
            if pattern in name_lower:
                return False
        
        # 检查内容特征
        if content_preview:
            # 包含标题的文件更可能是主文件
            if re.search(r'^#\s+', content_preview, re.MULTILINE):
                return True
            
            # 包含大量文本的文件更可能是主文件
            if len(content_preview.replace('\n', '').strip()) > 200:
                return True
        
        return True
    
    def select_best_markdown(self, directory: str, project_name: str = None) -> Optional[str]:
        """
        自动选择最佳的markdown文件
        
        Args:
            directory: 目录路径
            project_name: 项目名称（用于匹配文件名）
            
        Returns:
            最佳markdown文件路径，如果没有找到则返回None
        """
        md_files = self.find_markdown_files(directory)
        
        if not md_files:
            return None
        
        if len(md_files) == 1:
            return md_files[0]['path']
        
        # 多个文件时的选择策略
        best_file = None
        best_score = 0
        
        for file_info in md_files:
            score = self._calculate_file_score(file_info, project_name)
            
            if score > best_score:
                best_score = score
                best_file = file_info['path']
        
        return best_file
    
    def _calculate_file_score(self, file_info: Dict[str, str], project_name: str = None) -> float:
        """
        计算文件选择分数
        
        Args:
            file_info: 文件信息
            project_name: 项目名称
            
        Returns:
            文件分数
        """
        score = 0.0
        
        # 基础分数：文件大小（大文件更可能是主文件）
        size_score = min(file_info['size'] / 10000, 1.0)  # 10KB为满分
        score += size_score * 0.3
        
        # 主文件可能性
        if file_info['is_likely_main']:
            score += 0.4
        
        # 项目名称匹配度
        if project_name:
            name_similarity = SequenceMatcher(
                None, 
                project_name.lower(), 
                file_info['stem'].lower()
            ).ratio()
            score += name_similarity * 0.3
        
        return score
    
    def interactive_markdown_selection(self, directory: str, project_name: str = None) -> Optional[str]:
        """
        交互式markdown文件选择
        
        Args:
            directory: 目录路径
            project_name: 项目名称
            
        Returns:
            选择的markdown文件路径，如果取消则返回None
        """
        md_files = self.find_markdown_files(directory)
        
        if not md_files:
            print(f"❌ 在目录 {directory} 中没有找到markdown文件")
            return None
        
        if len(md_files) == 1:
            file_path = md_files[0]['path']
            print(f"✅ 自动选择唯一的markdown文件: {Path(file_path).name}")
            return file_path
        
        # 多个文件时显示选择菜单
        print(f"📄 在项目中找到 {len(md_files)} 个markdown文件:")
        
        for i, file_info in enumerate(md_files, 1):
            size_kb = file_info['size'] / 1024
            main_indicator = " [主文件]" if file_info['is_likely_main'] else ""
            print(f"  {i}. {file_info['name']} ({size_kb:.1f}KB){main_indicator}")
        
        # 自动推荐最佳文件
        best_file = self.select_best_markdown(directory, project_name)
        if best_file:
            best_index = next(
                (i for i, f in enumerate(md_files, 1) if f['path'] == best_file), 
                1
            )
            print(f"\n💡 推荐选择: {best_index}")
        
        try:
            choice = input(f"\n请选择文件编号 (1-{len(md_files)}) 或按回车使用推荐: ").strip()
            
            if not choice and best_file:
                print(f"✅ 使用推荐文件: {Path(best_file).name}")
                return best_file
            
            if choice:
                index = int(choice) - 1
                if 0 <= index < len(md_files):
                    selected_file = md_files[index]['path']
                    print(f"✅ 选择文件: {Path(selected_file).name}")
                    return selected_file
                else:
                    print("❌ 无效的选择")
                    return None
            
            return None
            
        except (ValueError, KeyboardInterrupt):
            print("❌ 操作已取消")
            return None
    
    def validate_markdown_file(self, file_path: str) -> Tuple[bool, str]:
        """
        验证markdown文件是否有效
        
        Args:
            file_path: 文件路径
            
        Returns:
            (是否有效, 错误信息)
        """
        path = Path(file_path)
        
        if not path.exists():
            return False, f"文件不存在: {file_path}"
        
        if not path.is_file():
            return False, f"不是文件: {file_path}"
        
        if path.suffix.lower() != '.md':
            return False, f"不是markdown文件: {file_path}"
        
        try:
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read(1000)  # 读取前1000字符
                if not content.strip():
                    return False, "文件内容为空"
        except Exception as e:
            return False, f"无法读取文件: {e}"
        
        return True, ""


def main():
    """测试markdown检测器功能"""
    detector = MarkdownDetector()
    
    # 测试目录
    test_dirs = [
        "documents/extracted/tiku_20250615_003402",
        "documents/extracted/冲刺版_2024北sen整理题库数学百题_20250614_223822"
    ]
    
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            print(f"\n测试目录: {test_dir}")
            md_files = detector.find_markdown_files(test_dir)
            
            if md_files:
                print(f"找到 {len(md_files)} 个markdown文件:")
                for file_info in md_files:
                    main_flag = " [主文件]" if file_info['is_likely_main'] else ""
                    print(f"  - {file_info['name']} ({file_info['size']} bytes){main_flag}")
                
                best = detector.select_best_markdown(test_dir)
                if best:
                    print(f"推荐文件: {Path(best).name}")
            else:
                print("没有找到markdown文件")


if __name__ == "__main__":
    main()
