#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内容过滤器 - 用于过滤垃圾图片和不需要的文字内容
使用更精确的图像相似度算法
"""
import os
import numpy as np
from PIL import Image
import logging
import configparser
from typing import List, Dict, Any, Optional

# 尝试导入OpenCV，如果不可用则使用PIL
try:
    import cv2
    HAS_OPENCV = True
except ImportError:
    HAS_OPENCV = False
    logging.warning("OpenCV未安装，图片相似度比较功能将使用PIL实现")

logger = logging.getLogger(__name__)

def calculate_phash(image_array: np.ndarray, hash_size: int = 8) -> int:
    """
    计算图像的感知哈希值
    
    Args:
        image_array: 图像数组
        hash_size: 哈希大小
        
    Returns:
        int: 感知哈希值
    """
    # 缩放到hash_size x hash_size
    if len(image_array.shape) == 3:
        # 转换为灰度
        gray = np.mean(image_array, axis=2)
    else:
        gray = image_array
    
    # 使用PIL进行缩放
    from PIL import Image
    img_pil = Image.fromarray(gray.astype(np.uint8))
    img_resized = img_pil.resize((hash_size, hash_size), Image.Resampling.LANCZOS)
    resized_array = np.array(img_resized)
    
    # 计算DCT (简化版本)
    dct = np.fft.fft2(resized_array)
    
    # 取左上角的低频部分
    low_freq = np.abs(dct[:hash_size//2, :hash_size//2])
    
    # 计算平均值
    avg = np.mean(low_freq)
    
    # 生成哈希
    hash_bits = []
    for i in range(hash_size//2):
        for j in range(hash_size//2):
            hash_bits.append(1 if low_freq[i, j] > avg else 0)
    
    # 转换为整数
    hash_value = 0
    for bit in hash_bits:
        hash_value = (hash_value << 1) | bit
    
    return hash_value

def hamming_distance(hash1: int, hash2: int) -> int:
    """
    计算两个哈希值的汉明距离
    
    Args:
        hash1: 第一个哈希值
        hash2: 第二个哈希值
        
    Returns:
        int: 汉明距离
    """
    return bin(hash1 ^ hash2).count('1')

def calculate_template_match_score(img1: np.ndarray, img2: np.ndarray) -> float:
    """
    使用模板匹配计算相似度
    
    Args:
        img1: 第一个图像
        img2: 第二个图像
        
    Returns:
        float: 相似度分数 (0-1)
    """
    try:
        if HAS_OPENCV:
            # 确保图像尺寸一致
            h1, w1 = img1.shape[:2]
            h2, w2 = img2.shape[:2]
            
            # 将较大的图像作为模板，较小的作为搜索图像
            if h1 * w1 > h2 * w2:
                template = img2
                search_img = img1
            else:
                template = img1
                search_img = img2
            
            # 如果模板比搜索图像大，交换它们
            if template.shape[0] > search_img.shape[0] or template.shape[1] > search_img.shape[1]:
                template, search_img = search_img, template
            
            # 执行模板匹配
            result = cv2.matchTemplate(search_img, template, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, _ = cv2.minMaxLoc(result)
            
            return max_val
        else:
            # 使用简单的像素级相关性
            # 首先将图像调整为相同大小
            min_h = min(img1.shape[0], img2.shape[0])
            min_w = min(img1.shape[1], img2.shape[1])
            
            img1_resized = np.array(Image.fromarray(img1).resize((min_w, min_h)))
            img2_resized = np.array(Image.fromarray(img2).resize((min_w, min_h)))
            
            # 计算归一化相关系数
            corr = np.corrcoef(img1_resized.flatten(), img2_resized.flatten())[0, 1]
            
            return max(0, corr) if not np.isnan(corr) else 0
            
    except Exception as e:
        logger.warning(f"模板匹配计算失败: {e}")
        return 0

class ContentFilter:
    """内容过滤器，用于过滤不需要的图片和文字内容"""
    
    def __init__(self, trash_image_dir: str = "trash_image", 
                 filtered_images_dir: str = "filtered_images",
                 config_file: str = "filter_config.ini"):
        """
        初始化内容过滤器
        
        Args:
            trash_image_dir: 垃圾图片目录
            filtered_images_dir: 被过滤图片保存目录
            config_file: 配置文件路径
        """
        self.trash_image_dir = trash_image_dir
        self.filtered_images_dir = filtered_images_dir
        
        # 默认配置
        self.similarity_threshold = 0.9
        self.blocked_texts = [
            "专业笔试助攻",
            "代做:jobhelp101",
            "jobhelp101",
            "专业笔试助攻\\代做:jobhelp101"
        ]
        self.verbose_filtering = True
        
        # 加载配置文件
        self._load_config(config_file)
        
        # 创建过滤图片目录
        os.makedirs(self.filtered_images_dir, exist_ok=True)
        
        self.trash_images = []
        self._load_trash_images()
    
    def _load_config(self, config_file: str):
        """加载配置文件"""
        if os.path.exists(config_file):
            try:
                config = configparser.ConfigParser()
                config.read(config_file, encoding='utf-8')
                
                # 内容过滤配置
                if 'CONTENT_FILTER' in config:
                    section = config['CONTENT_FILTER']
                    self.similarity_threshold = section.getfloat('similarity_threshold', 0.9)
                    self.trash_image_dir = section.get('trash_image_dir', self.trash_image_dir)
                    self.filtered_images_dir = section.get('filtered_images_dir', self.filtered_images_dir)
                    
                    # 解析被屏蔽文字列表
                    blocked_texts_str = section.get('blocked_texts', '')
                    if blocked_texts_str:
                        self.blocked_texts = [text.strip() for text in blocked_texts_str.split(',')]
                
                # 日志配置
                if 'LOGGING' in config:
                    section = config['LOGGING']
                    self.verbose_filtering = section.getboolean('verbose_filtering', True)
                
                logger.info(f"加载配置文件: {config_file}")
                logger.info(f"相似度阈值: {self.similarity_threshold}")
                logger.info(f"被屏蔽文字: {len(self.blocked_texts)} 个")
                
            except Exception as e:
                logger.warning(f"加载配置文件失败: {e}，使用默认配置")
        else:
            logger.info(f"配置文件不存在: {config_file}，使用默认配置")
    
    def _load_trash_images(self):
        """加载垃圾图片模板"""
        if not os.path.exists(self.trash_image_dir):
            logger.warning(f"垃圾图片目录不存在: {self.trash_image_dir}")
            return
        
        for filename in os.listdir(self.trash_image_dir):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                image_path = os.path.join(self.trash_image_dir, filename)
                try:
                    if HAS_OPENCV:
                        # 使用OpenCV加载图片
                        img = cv2.imread(image_path)
                        if img is not None:
                            # 转换为灰度图
                            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                            # 计算感知哈希
                            phash = calculate_phash(gray)
                            self.trash_images.append({
                                'path': image_path,
                                'filename': filename,
                                'image_array': gray,
                                'phash': phash,
                                'shape': gray.shape
                            })
                    else:
                        # 使用PIL加载图片
                        img = Image.open(image_path)
                        if img.mode != 'L':
                            img = img.convert('L')  # 转换为灰度
                        img_array = np.array(img)
                        # 计算感知哈希
                        phash = calculate_phash(img_array)
                        self.trash_images.append({
                            'path': image_path,
                            'filename': filename,
                            'image_array': img_array,
                            'phash': phash,
                            'shape': img_array.shape
                        })
                    
                    logger.info(f"加载垃圾图片模板: {filename}")
                except Exception as e:
                    logger.warning(f"加载垃圾图片失败 {filename}: {e}")
    
    def get_image_similarity(self, image_data: bytes) -> Dict[str, Any]:
        """
        检查图片与垃圾图片的相似度
        
        Args:
            image_data: 图片二进制数据
            
        Returns:
            Dict: 包含相似度信息的字典
        """
        result = {
            'is_similar': False,
            'similarity': 0.0,
            'matched_template': None,
            'method': 'combined'
        }
        
        if not self.trash_images:
            return result
        
        try:
            # 加载测试图像
            if HAS_OPENCV:
                nparr = np.frombuffer(image_data, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                if img is None:
                    return result
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            else:
                from io import BytesIO
                img = Image.open(BytesIO(image_data))
                if img.mode != 'L':
                    img = img.convert('L')
                gray = np.array(img)
            
            # 计算测试图像的感知哈希
            test_phash = calculate_phash(gray)
            
            max_similarity = 0.0
            matched_template = None
            
            # 与每个垃圾图片模板比较
            for trash_img in self.trash_images:
                # 方法1: 感知哈希比较
                hamming_dist = hamming_distance(test_phash, trash_img['phash'])
                max_hamming = 16  # 4x4 hash的最大汉明距离
                phash_similarity = 1.0 - (hamming_dist / max_hamming)
                
                # 方法2: 模板匹配
                template_similarity = calculate_template_match_score(gray, trash_img['image_array'])
                
                # 综合相似度 (感知哈希权重0.3，模板匹配权重0.7)
                combined_similarity = 0.3 * phash_similarity + 0.7 * template_similarity
                
                if self.verbose_filtering:
                    logger.debug(f"与 {trash_img['filename']} 比较: "
                               f"感知哈希={phash_similarity:.3f}, "
                               f"模板匹配={template_similarity:.3f}, "
                               f"综合={combined_similarity:.3f}")
                
                if combined_similarity > max_similarity:
                    max_similarity = combined_similarity
                    matched_template = trash_img['filename']
            
            result['similarity'] = max_similarity
            result['matched_template'] = matched_template
            result['is_similar'] = max_similarity > self.similarity_threshold
            
            if result['is_similar'] and self.verbose_filtering:
                logger.info(f"发现相似垃圾图片，相似度: {max_similarity:.3f}, 匹配模板: {matched_template}")
            
            return result
            
        except Exception as e:
            logger.warning(f"图片相似度检测失败: {e}")
            return result
    
    def save_filtered_image(self, image_data: bytes, similarity_info: Dict[str, Any], 
                           page_num: int, img_index: int) -> Optional[str]:
        """
        保存被过滤的图片到指定目录
        
        Args:
            image_data: 图片二进制数据
            similarity_info: 相似度信息
            page_num: 页码
            img_index: 图片索引
            
        Returns:
            str: 保存的文件路径，如果保存失败返回None
        """
        if not similarity_info['is_similar']:
            return None
        
        try:
            # 生成文件名，包含相似度信息
            similarity = similarity_info['similarity']
            matched_template = similarity_info['matched_template']
            template_name = os.path.splitext(matched_template)[0] if matched_template else "unknown"
            
            filename = f"filtered_page{page_num}_img{img_index}_sim{similarity:.3f}_template_{template_name}.png"
            filepath = os.path.join(self.filtered_images_dir, filename)
            
            # 保存图片
            with open(filepath, 'wb') as f:
                f.write(image_data)
            
            if self.verbose_filtering:
                logger.info(f"保存被过滤图片: {filename}")
            return filepath
            
        except Exception as e:
            logger.warning(f"保存被过滤图片失败: {e}")
            return None
    
    def is_similar_to_trash_image(self, image_data: bytes) -> bool:
        """
        检查图片是否与垃圾图片相似 (向后兼容方法)
        """
        similarity_info = self.get_image_similarity(image_data)
        return similarity_info['is_similar']
    
    def contains_blocked_text(self, text: str) -> bool:
        """
        检查文本是否包含被屏蔽的内容
        """
        if not text:
            return False
        
        text_lower = text.lower().strip()
        
        for blocked_text in self.blocked_texts:
            if blocked_text.lower() in text_lower:
                if self.verbose_filtering:
                    logger.info(f"发现被屏蔽的文字内容: {blocked_text}")
                return True
        
        return False
    
    def filter_text_blocks(self, text_blocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤文本块，移除包含被屏蔽内容的文本
        """
        filtered_blocks = []
        
        for block in text_blocks:
            text_content = block.get('content', '') or block.get('text', '')
            
            if not self.contains_blocked_text(text_content):
                filtered_blocks.append(block)
            elif self.verbose_filtering:
                logger.info(f"过滤掉包含屏蔽内容的文本块: {text_content[:50]}...")
        
        return filtered_blocks
    
    def filter_images(self, images: List[Dict[str, Any]], page_num: int = 0) -> List[Dict[str, Any]]:
        """
        过滤图片，移除与垃圾图片相似的图片，并保存被过滤的图片
        """
        filtered_images = []
        
        for img_info in images:
            image_data = img_info.get('data')
            img_index = img_info.get('img_index', 0)
            
            if image_data:
                similarity_info = self.get_image_similarity(image_data)
                
                if similarity_info['is_similar']:
                    # 保存被过滤的图片
                    saved_path = self.save_filtered_image(image_data, similarity_info, page_num, img_index)
                    
                    if self.verbose_filtering:
                        logger.info(f"过滤掉相似的垃圾图片: {img_info.get('filename', '未知')}, "
                                   f"相似度: {similarity_info['similarity']:.3f}, "
                                   f"匹配模板: {similarity_info['matched_template']}")
                    continue
            
            filtered_images.append(img_info)
        
        return filtered_images
    
    def filter_page_content(self, page_content: Dict[str, Any], page_num: int = 0) -> Dict[str, Any]:
        """
        过滤页面内容
        """
        # 过滤文本块
        if 'text_blocks' in page_content:
            page_content['text_blocks'] = self.filter_text_blocks(page_content['text_blocks'])
        
        # 过滤图片
        if 'images' in page_content:
            page_content['images'] = self.filter_images(page_content['images'], page_num)
            # 同时更新image_blocks
            page_content['image_blocks'] = page_content['images']
        
        return page_content
