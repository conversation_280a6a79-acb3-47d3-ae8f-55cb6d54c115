; Multi-Channel AI Service Configuration
; 多通道AI服务配置文件

[DEFAULT]
; Document Processing Settings
; 文档处理设置
BASE_DIR = documents
PDF_TOP_MARGIN = 50.0
PDF_BOTTOM_MARGIN = 50.0

; Legacy compatibility (will be overridden by document processor)
INPUT_PDF = None
INPUT_FILE = tiku_md/tiku.md
OUTPUT_FILE = ankiflashcards.txt
LINES_TO_SKIP = 0

; Processing Settings
; 处理设置
CHUNK_SIZE = 500
CHUNK_STRIDE = 450
MAX_WORKERS = 10

; Cache Settings
; 缓存设置
CACHE_DIR = cache

; File Naming Settings
; 文件命名设置
USE_TIMESTAMP = true
CLEAN_FILENAMES = true

; AI Channel 1 - DeepSeek (Primary)
; AI通道1 - DeepSeek（主要）
[CHANNEL_1]
API_BASE_URL = https://v2.voct.top/v1
API_PROVIDER = openai
API_KEY = fo-_to6t48Uxu76weXPT2rWE7U1mePzNdIF
MODEL_NAME = deepseek-ai/DeepSeek-V3
REQUEST_TIMEOUT = 120
WEIGHT = 2
ENABLED = true
MAX_CONCURRENT = 5
USE_STREAMING = true

; AI Channel 2 - Backup Channel (Disabled by default)
; AI通道2 - 备用通道（默认禁用）
[CHANNEL_2]
API_BASE_URL = https://tbai.xin/v1
API_PROVIDER = openai
API_KEY = sk-5rVVgbmBRHyN7aNwJp1Ls4weBNXaHVOnj5yvOGJSI97IxgcM
MODEL_NAME = deepseek-v3
REQUEST_TIMEOUT = 120
WEIGHT = 1
ENABLED = true
MAX_CONCURRENT = 3
USE_STREAMING = false

; Environment Variable Examples:
; 环境变量示例：
; export CHANNEL_1_API_KEY="your_actual_key"
; export CHANNEL_2_ENABLED="true"
; export MAX_WORKERS="15"