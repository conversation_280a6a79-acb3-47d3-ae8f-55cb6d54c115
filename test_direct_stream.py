#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_direct_stream.py

直接测试流式调用
"""

import json
import requests
import configparser
import os


def load_config():
    """简化的配置加载"""
    parser = configparser.ConfigParser()
    parser.read("config.ini", encoding="utf-8")
    cfg = parser["DEFAULT"]
    
    return {
        "api_base_url": cfg.get("API_BASE_URL", "").rstrip("/"),
        "api_key": cfg.get("API_KEY", ""),
        "model_name": cfg.get("MODEL_NAME", ""),
        "api_provider": cfg.get("API_PROVIDER", "openai"),
        "request_timeout": int(cfg.get("REQUEST_TIMEOUT", "120")),
        "cache_dir": cfg.get("CACHE_DIR", "cache"),
    }


def create_ai_prompt():
    """创建 AI 指令"""
    system_prompt = (
        "你是一个精通数据提取的AI助手，专注于从结构化和非结构化的文本中准确解析教育内容。"
        "你的任务是从给定的文本中提取完整的问答对，并严格按照指定格式返回。"
    )
    
    task_prompt = """请严格按照以下规则，分析提供的 Markdown 文本片段，并提取其中所有完整的'问题-答案'对：

**提取规则：**
1. **完整性优先：** 如果文本片段末尾的问题或答案不完整，必须彻底忽略该条目，绝不猜测或补全。
2. **格式保留：** front（问题）和 back（答案）字段的内容应尽量保留原始的 Markdown 格式，如列表、代码块等。对于换行，请直接保留 \\n。
3. **图片处理：** 若内容中包含 Markdown 图片格式 ![alt text](image_name.png)，则直接提取并输出图片的文件名 image_name.png。
4. **内容忽略：** 在提取过程中，必须忽略所有出现的无用营销信息，特别是 "专业笔试助攻\\代做:jobhelp101"。

**输出格式要求：**
你必须将所有提取出的'问题-答案'对整合到一个单一的JSON对象中。此JSON对象必须包含一个根键 "questions"，其值为一个JSON对象列表。列表中的每个对象代表一道独立的题目，且必须包含 "front" 和 "back" 两个键。

如果当前文本块中没有发现任何完整的问答对，则返回 {"questions": []}。

**JSON 格式示例：**
{
  "questions": [
    {
      "front": "这是题目的完整内容，包括所有选项，例如：\\n- A. 选项一\\n- B. 选项二",
      "back": "这是题目的答案和可能的解析。"
    }
  ]
}

**重要：** 只返回JSON格式的数据，不要包含任何其他文字说明。"""
    
    return system_prompt, task_prompt


def test_stream_api():
    """测试流式 API 调用"""
    print("🚀 测试流式 AI API 调用")
    print("=" * 50)
    
    cfg = load_config()
    
    url = cfg["api_base_url"] + "/chat/completions"
    headers = {
        "Authorization": f"Bearer {cfg['api_key']}",
        "Content-Type": "application/json",
    }
    
    system_prompt, task_prompt = create_ai_prompt()
    
    test_chunk = """
1. 根据以下图形规律，下一个图形应该是？
![page_1_img_1.png](images/page_1_img_1.png)
A. 上图第一项
B. 上图第二项  
C. 上图第三项
D. 上图第四项
答：C
解：把线条分为上下移动和左右移动，横线从下往上移动1个单位，竖线从左向右移动1个单位（移到最右边就再回到最左边）

2. 空缺图形应该是？
专业笔试助攻\\代做:jobhelp101
![page_2_img_1.png](images/page_2_img_1.png)
A. 上图第一项
B. 上图第二项
C. 上图第三项  
D. 上图第四项
答：D
解：图形1+2=3，第一排图形取最外层图形，第二排的图形为第一排外层图形里面的图形，第三排图形是第一排外层+第二排里面的图形
"""
    
    # 根据 API Provider 构建 payload
    if cfg["api_provider"].lower() == "siliconflow":
        # SiliconFlow 使用单个用户消息
        full_prompt = f"{system_prompt}\n\n{task_prompt}\n\n---\n\n{test_chunk}"
        payload = {
            "model": cfg["model_name"],
            "messages": [{"role": "user", "content": full_prompt}],
            "temperature": 0.3,
            "max_tokens": 4000,
            "stream": True,
        }
    else:  # 默认为 openai 格式
        payload = {
            "model": cfg["model_name"],
            "messages": [
                {"role": "system", "content": system_prompt + "\n" + task_prompt},
                {"role": "user", "content": test_chunk},
            ],
            "temperature": 0.3,
            "max_tokens": 4000,
            "stream": True,
        }
    
    print(f"📡 API 端点: {url}")
    print(f"🤖 模型: {cfg['model_name']}")
    print(f"📋 提供商: {cfg['api_provider']}")
    print(f"📝 测试内容长度: {len(test_chunk)} 字符")
    print("\n🚀 开始流式调用...")
    
    try:
        response = requests.post(
            url,
            headers=headers,
            json=payload,
            timeout=cfg["request_timeout"],
            stream=True
        )
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            content_parts = []
            chunk_count = 0
            
            print("📥 正在接收流式响应...")
            
            for line in response.iter_lines():
                if line:
                    chunk_count += 1
                    line = line.decode('utf-8')
                    
                    if line.startswith('data: '):
                        data = line[6:]
                        if data == '[DONE]':
                            print("🏁 流式响应完成")
                            break
                        
                        try:
                            chunk_data = json.loads(data)
                            if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                                delta = chunk_data['choices'][0].get('delta', {})
                                  # 检查 content 字段
                                if 'content' in delta and delta['content'] is not None:
                                    content_parts.append(delta['content'])
                                    print(f"Content: {delta['content']}", end='', flush=True)
                                # 对于推理内容，我们不添加到 content_parts，只显示
                                elif 'reasoning_content' in delta and delta['reasoning_content'] is not None:
                                    print(f"Reasoning: {delta['reasoning_content']}", end='', flush=True)
                                    
                        except json.JSONDecodeError:
                            continue
            
            print(f"\n\n📊 处理了 {chunk_count} 个数据块")
            
            # 合并所有内容
            full_content = ''.join(content_parts).strip()
            print(f"📝 完整内容长度: {len(full_content)} 字符")
            print(f"📄 完整内容预览: {full_content[:500]}...")
            
            if full_content:
                # 尝试解析为 JSON
                try:
                    # 清理内容
                    if full_content.startswith("```json"):
                        full_content = full_content[7:]
                    if full_content.endswith("```"):
                        full_content = full_content[:-3]
                    full_content = full_content.strip()
                    
                    parsed = json.loads(full_content)
                    questions = parsed.get("questions", [])
                    
                    print(f"\n✅ 成功解析 JSON，提取到 {len(questions)} 个问答对:")
                    
                    for i, qa in enumerate(questions, 1):
                        print(f"\n--- 问题 {i} ---")
                        print(f"Front: {qa.get('front', '')}")
                        print(f"Back: {qa.get('back', '')}")
                        print("-" * 40)
                    
                    return True
                    
                except json.JSONDecodeError as e:
                    print(f"❌ JSON 解析失败: {e}")
                    print(f"原始内容: {full_content}")
                    return False
            else:
                print("❌ 没有接收到任何内容")
                return False
        else:
            print(f"❌ API 请求失败: {response.status_code}")
            print(f"错误信息: {response.text[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def main():
    success = test_stream_api()
    
    print("\n" + "=" * 50)
    print(f"测试结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("🎉 流式 API 调用工作正常!")
        print("💡 现在可以集成到主程序中")
    else:
        print("🔧 需要进一步调试")


if __name__ == "__main__":
    main()
