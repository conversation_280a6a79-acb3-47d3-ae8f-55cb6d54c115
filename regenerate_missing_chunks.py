#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
regenerate_missing_chunks_fixed.py

根据CSV文件中的问题块信息，重新调用AI生成对应的JSON文件（修复版本）
"""

import os
import sys
import json
import csv
import logging
import configparser
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
from ai_service import AIService


def load_config(config_path: str = "config.ini") -> dict:
    """加载配置文件"""
    parser = configparser.ConfigParser()
    
    if not os.path.exists(config_path):
        logging.error(f"配置文件不存在: {config_path}")
        sys.exit(1)
    
    try:
        parser.read(config_path, encoding="utf-8")
    except Exception as e:
        logging.error(f"读取配置文件失败: {e}")
        sys.exit(1)
    
    cfg = parser["DEFAULT"]

    def get(name: str, cast, default=None):
        val = os.getenv(name, cfg.get(name, fallback=None))
        return cast(val) if val is not None else default

    config = {
        "input_file": get("INPUT_FILE", str, "tiku.md"),
        "lines_to_skip": get("LINES_TO_SKIP", int, 13),
        "chunk_size": get("CHUNK_SIZE", int, 500),
        "chunk_stride": get("CHUNK_STRIDE", int, 450),
        "api_base_url": get("API_BASE_URL", str, "").rstrip("/"),
        "api_key": get("API_KEY", str, ""),
        "model_name": get("MODEL_NAME", str, ""),
        "max_workers": get("MAX_WORKERS", int, 3),  # 降低并发数避免速率限制
        "request_timeout": get("REQUEST_TIMEOUT", int, 120),
        "cache_dir": get("CACHE_DIR", str, "cache"),
        "api_provider": get("API_PROVIDER", str, "openai"),
    }
    
    # 验证必要的配置
    required_fields = ["api_key", "api_base_url", "model_name"]
    for field in required_fields:
        if not config[field]:
            logging.error(f"{field.upper()} 未配置")
            sys.exit(1)
    
    return config


def setup_logging(log_file: str = "regenerate.log"):
    """初始化日志到控制台和文件"""
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    
    # 避免重复添加处理器
    if logger.handlers:
        logger.handlers.clear()
    
    fmt = logging.Formatter("%(asctime)s [%(levelname)s] %(message)s")

    # 文件处理器
    fh = logging.FileHandler(log_file, encoding="utf-8")
    fh.setLevel(logging.DEBUG)
    fh.setFormatter(fmt)

    # 控制台处理器
    ch = logging.StreamHandler(sys.stdout)
    ch.setLevel(logging.INFO)
    ch.setFormatter(fmt)

    logger.addHandler(fh)
    logger.addHandler(ch)


def read_input_lines(path: str, skip: int) -> List[str]:
    """读取文件内容并跳过前 skip 行"""
    try:
        with open(path, "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        # 跳过前 skip 行
        lines = lines[skip:]
        
        # 移除空行和仅包含空格的行
        lines = [line.rstrip() for line in lines if line.strip()]
        
        logging.info(f"成功读取文件 {path}，跳过前 {skip} 行，有效行数: {len(lines)}")
        return lines
        
    except FileNotFoundError:
        logging.error(f"输入文件不存在: {path}")
        sys.exit(1)
    except IOError as e:
        logging.error(f"读取输入文件失败: {e}")
        sys.exit(1)


def make_chunks(lines: List[str], size: int, stride: int) -> Dict[int, str]:
    """创建文本块，返回字典映射 chunk_idx -> text"""
    chunks = {}
    start = 0
    chunk_idx = 0
    
    while start < len(lines):
        end = min(start + size, len(lines))
        chunk_lines = lines[start:end]
        chunk_text = "\n".join(chunk_lines)
        chunks[chunk_idx] = chunk_text
        
        if end >= len(lines):
            break
        
        chunk_idx += 1
        start += stride
    
    return chunks


def read_problematic_chunks_csv(csv_path: str) -> List[Dict]:
    """读取问题块CSV文件"""
    if not os.path.exists(csv_path):
        logging.error(f"CSV文件不存在: {csv_path}")
        sys.exit(1)
    
    chunks_to_regenerate = []
    
    try:
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                chunks_to_regenerate.append({
                    'chunk_idx': int(row['chunk_idx']),  # 正确的列名
                    'missing_images_count': int(row['missing_images_count']),
                    'missing_images': row['missing_images'].split('; ') if row['missing_images'].strip() else []  # 正确的分隔符
                })
        
        logging.info(f"从CSV文件读取到 {len(chunks_to_regenerate)} 个需要重新生成的块")
        return chunks_to_regenerate
        
    except Exception as e:
        logging.error(f"读取CSV文件失败: {e}")
        sys.exit(1)


def regenerate_chunk(chunk_info: Dict, chunk_map: Dict[int, str], cfg: dict) -> bool:
    """重新生成单个chunk的JSON文件
    
    Args:
        chunk_info: CSV中的chunk信息，包含chunk_idx
        chunk_map: chunk索引到文本的映射
        cfg: 配置字典
        
    Returns:
        bool: 是否成功重新生成
    """
    chunk_idx = chunk_info['chunk_idx']
    
    if chunk_idx not in chunk_map:
        logging.error(f"找不到块 {chunk_idx} 对应的文本内容")
        return False
    
    try:
        chunk_text = chunk_map[chunk_idx]
          # 使用AI服务重新生成
        ai_service = AIService(cfg)
        success = ai_service.call_api(chunk_text, chunk_idx, cfg["cache_dir"])
        
        if success:
            logging.info(f"成功重新生成块 {chunk_idx}")
            return True
        else:
            logging.error(f"重新生成块 {chunk_idx} 失败")
            return False
            
    except Exception as e:
        logging.error(f"重新生成块 {chunk_idx} 时发生异常: {e}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("重新生成缺失图片的Chunk JSON文件")
    print("=" * 60)
    
    setup_logging()
    cfg = load_config()
    
    # 检查CSV文件
    csv_file = "problematic_chunks.csv"
    if not os.path.exists(csv_file):
        logging.error(f"请先运行检查程序生成 {csv_file} 文件")
        print(f"❌ CSV文件不存在: {csv_file}")
        print("请先运行 check_missing_images.py 生成问题块列表")
        sys.exit(1)
    
    # 读取需要重新生成的chunk信息
    chunks_to_regenerate = read_problematic_chunks_csv(csv_file)
    
    if not chunks_to_regenerate:
        logging.info("没有需要重新生成的chunk")
        print("✅ 没有需要重新生成的chunk")
        return
    
    # 读取原始文本并创建chunk映射
    lines = read_input_lines(cfg["input_file"], cfg["lines_to_skip"])
    chunk_map = make_chunks(lines, cfg["chunk_size"], cfg["chunk_stride"])
    
    # 确认用户操作
    print(f"\n发现 {len(chunks_to_regenerate)} 个需要重新生成的块")
    print("这将覆盖现有的JSON文件（原文件会被备份为.backup）")
    
    confirm = input("是否继续？(y/N): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    logging.info(f"开始重新生成 {len(chunks_to_regenerate)} 个chunk...")
    
    # 并发重新生成chunk
    success_count = 0
    
    with ThreadPoolExecutor(max_workers=cfg["max_workers"]) as executor:
        # 提交所有任务
        futures = {
            executor.submit(regenerate_chunk, chunk_info, chunk_map, cfg): chunk_info['chunk_idx']
            for chunk_info in chunks_to_regenerate
        }
        
        # 显示进度并等待结果
        for future in tqdm(as_completed(futures), total=len(futures), desc="重新生成进度"):
            chunk_idx = futures[future]
            try:
                success = future.result()
                if success:
                    success_count += 1
            except Exception as e:
                logging.error(f"处理块 {chunk_idx} 时发生异常: {e}")
    
    logging.info("=" * 50)
    logging.info("重新生成完成！")
    logging.info(f"成功重新生成: {success_count}/{len(chunks_to_regenerate)} 个chunk")
    logging.info("=" * 50)
    
    print(f"\n✅ 重新生成完成！")
    print(f"📊 成功: {success_count}/{len(chunks_to_regenerate)} 个chunk")
    
    if success_count < len(chunks_to_regenerate):
        print(f"⚠️  有 {len(chunks_to_regenerate) - success_count} 个chunk重新生成失败，请查看日志")
    
    print(f"📝 建议重新运行 check_missing_images.py 验证结果")


if __name__ == "__main__":
    main()
