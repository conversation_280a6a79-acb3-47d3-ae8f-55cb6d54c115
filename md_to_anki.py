#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
md_to_anki_fixed.py

修复后的版本，包含正确的流式处理
"""

import os
import sys
import json
import time
import logging
import configparser
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import requests
from requests.exceptions import RequestException, Timeout, HTTPError
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import random
from ai_service import AIService
from anki_generator import AnkiGenerator


def load_config(config_path: str = "config.ini") -> dict:
    """加载配置优先级：环境变量 > config.ini"""
    parser = configparser.ConfigParser()
    
    # 检查配置文件是否存在
    if not os.path.exists(config_path):
        logging.error(f"配置文件不存在: {config_path}")
        sys.exit(1)
    
    try:
        parser.read(config_path, encoding="utf-8")
    except Exception as e:
        logging.error(f"读取配置文件失败: {e}")
        sys.exit(1)
    
    cfg = parser["DEFAULT"]

    def get(name: str, cast, default=None):
        val = os.getenv(name, cfg.get(name, fallback=None))
        return cast(val) if val is not None else default

    config = {
        "input_file": get("INPUT_FILE", str, "tiku.md"),
        "output_file": get("OUTPUT_FILE", str, "ankiflashcards.txt"),
        "lines_to_skip": get("LINES_TO_SKIP", int, 13),
        "chunk_size": get("CHUNK_SIZE", int, 500),
        "chunk_stride": get("CHUNK_STRIDE", int, 450),
        "api_base_url": get("API_BASE_URL", str, "").rstrip("/"),
        "api_key": get("API_KEY", str, ""),
        "model_name": get("MODEL_NAME", str, ""),
        "max_workers": get("MAX_WORKERS", int, 10),
        "request_timeout": get("REQUEST_TIMEOUT", int, 120),
        "cache_dir": get("CACHE_DIR", str, "cache"),
        "api_provider": get("API_PROVIDER", str, "openai"),
    }
    
    # 验证必要的配置
    if not config["api_key"]:
        logging.error("API_KEY 未配置，请在配置文件或环境变量中设置")
        sys.exit(1)
    
    if not config["api_base_url"]:
        logging.error("API_BASE_URL 未配置")
        sys.exit(1)
    
    if not config["model_name"]:
        logging.error("MODEL_NAME 未配置")
        sys.exit(1)
    
    return config


def setup_logging(log_file: str = "script.log"):
    """初始化日志到控制台和文件"""
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    
    # 避免重复添加处理器
    if logger.handlers:
        logger.handlers.clear()
    
    fmt = logging.Formatter("%(asctime)s [%(levelname)s] %(message)s")

    # 文件处理器
    fh = logging.FileHandler(log_file, encoding="utf-8")
    fh.setLevel(logging.DEBUG)
    fh.setFormatter(fmt)

    # 控制台处理器
    ch = logging.StreamHandler(sys.stdout)
    ch.setLevel(logging.INFO)
    ch.setFormatter(fmt)

    logger.addHandler(fh)
    logger.addHandler(ch)


def read_input_lines(path: str, skip: int) -> List[str]:
    """读取文件内容并跳过前 skip 行"""
    try:
        with open(path, "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        # 跳过前 skip 行
        lines = lines[skip:]
        
        # 移除空行和仅包含空格的行
        lines = [line.rstrip() for line in lines if line.strip()]
        
        logging.info(f"成功读取文件 {path}，跳过前 {skip} 行，有效行数: {len(lines)}")
        return lines
        
    except FileNotFoundError:
        logging.error(f"输入文件不存在: {path}")
        sys.exit(1)
    except IOError as e:
        logging.error(f"读取输入文件失败: {e}")
        sys.exit(1)


def make_chunks(lines: List[str], size: int, stride: int) -> List[Tuple[int, str]]:
    """使用滑动窗口将文本切分为重叠块，返回 (块索引, 文本) 列表"""
    chunks = []
    start = 0
    chunk_idx = 0
    
    while start < len(lines):
        end = min(start + size, len(lines))
        chunk_lines = lines[start:end]
        chunk_text = "\n".join(chunk_lines)
        chunks.append((chunk_idx, chunk_text))
        
        # 如果已经到达文件末尾，停止
        if end >= len(lines):
            break
        
        chunk_idx += 1
        start += stride
    
    logging.info(f"文本分块完成，共生成 {len(chunks)} 个块，平均每块约 {sum(len(text) for _, text in chunks) // len(chunks) if chunks else 0} 字符")
    return chunks


def call_ai_api(chunk: str, cfg: dict, idx: int) -> bool:
    """调用 AI API 提取问答对，并将结果存入缓存（支持流式调用）"""
    ai_service = AIService(cfg)
    return ai_service.call_api(chunk, idx, cfg["cache_dir"])


def main():
    """主函数"""
    print("=" * 60)
    print("Markdown to Anki 转换工具 (流式版本)")
    print("=" * 60)
    
    setup_logging()
    cfg = load_config()
    
    logging.info("=" * 50)
    logging.info("开始处理 Markdown to Anki 转换")
    logging.info(f"配置信息:")
    logging.info(f"  输入文件: {cfg['input_file']}")
    logging.info(f"  输出文件: {cfg['output_file']}")
    logging.info(f"  跳过行数: {cfg['lines_to_skip']}")
    logging.info(f"  块大小: {cfg['chunk_size']} 行")
    logging.info(f"  滑动步长: {cfg['chunk_stride']} 行")
    logging.info(f"  最大并发数: {cfg['max_workers']}")
    logging.info(f"  API 提供商: {cfg['api_provider']}")
    logging.info(f"  模型: {cfg['model_name']}")
    logging.info("=" * 50)

    # 创建缓存目录
    cache_path = Path(cfg["cache_dir"])
    cache_path.mkdir(exist_ok=True)
    logging.info(f"缓存目录: {cache_path.absolute()}")

    # 读取输入文件
    lines = read_input_lines(cfg["input_file"], cfg["lines_to_skip"])
    
    # 创建文本块
    chunks = make_chunks(lines, cfg["chunk_size"], cfg["chunk_stride"])
    if not chunks:
        logging.error("没有生成任何文本块，请检查输入文件和配置")
        sys.exit(1)

    # 并发处理文本块
    logging.info(f"开始并发处理 {len(chunks)} 个文本块...")
    
    with ThreadPoolExecutor(max_workers=cfg["max_workers"]) as executor:
        # 提交需要处理的任务（跳过已缓存的）
        futures = {}
        for idx, text in chunks:
            cache_file = cache_path / f"{idx}.json"
            if not cache_file.exists():
                future = executor.submit(call_ai_api, text, cfg, idx)
                futures[future] = idx

        # 显示进度并等待结果
        if futures:
            logging.info(f"需要处理 {len(futures)} 个新块，{len(chunks) - len(futures)} 个块已缓存")
            
            success_count = 0
            for fut in tqdm(as_completed(futures), total=len(futures), desc="AI 处理进度"):
                idx = futures[fut]
                try:
                    success = fut.result()
                    if success:
                        success_count += 1
                except Exception as e:
                    logging.error(f"块 {idx} 处理异常: {e}")
            
            logging.info(f"AI 处理完成: {success_count}/{len(futures)} 个块成功处理")
        else:
            logging.info("所有块都已缓存，跳过 AI 处理")    # 加载所有缓存结果并生成 Anki 文件
    logging.info("生成 Anki 文件...")
    anki_generator = AnkiGenerator(cfg["cache_dir"])
    stats = anki_generator.generate_anki_file(cfg["output_file"], len(chunks))
    
    if stats["total_cards"] == 0:
        logging.error("没有提取到任何问答对，请检查输入文件格式和 AI 指令")
        sys.exit(1)
    
    logging.info("=" * 50)
    logging.info("处理完成！")
    logging.info(f"成功提取 {stats['total_cards']} 个问答对")
    logging.info(f"其中 {stats['questions_with_images']} 个包含图片")
    logging.info(f"图片总数: {stats['total_images']}")
    logging.info(f"输出文件: {cfg['output_file']}")
    logging.info("=" * 50)
    
    print(f"\n✅ 转换完成！成功生成 {stats['total_cards']} 张 Anki 卡片")
    print(f"🖼️  其中 {stats['questions_with_images']} 张包含图片（共 {stats['total_images']} 个图片）")
    print(f"📁 输出文件: {cfg['output_file']}")


if __name__ == "__main__":
    main()
