# 多文档题库生成系统

一个专业的多文档处理和Anki闪卡生成系统，支持PDF、Word等多种文档格式的智能提取和转换。

## ✨ 核心特性

### 📄 多格式文档支持
- **PDF文档**: 智能文本和图片提取，支持页面边距控制
- **Word文档**: 支持.docx/.doc格式，提取文本、图片和表格
- **统一处理**: 所有文档类型使用相同的处理流程和命名规范

### 🗂️ 智能文件管理
- **项目化管理**: 每个文档生成独立的项目目录
- **标准化命名**: 自动清理文件名，生成时间戳项目名
- **分类存储**: 按类型分别存储原始文档、提取内容、图片、缓存等
- **批量处理**: 支持目录级别的批量文档处理

### 🎯 智能内容提取
- **文本提取**: 保持原始格式，智能段落识别
- **图片处理**: 标准化图片命名，自动生成Markdown引用
- **表格转换**: Word表格自动转换为Markdown表格格式
- **页面控制**: PDF提取支持页眉页脚过滤

### 🃏 AI驱动的Anki生成
- **流式处理**: 支持大文档的分块处理和进度跟踪
- **智能缓存**: 避免重复处理，支持断点续传
- **多线程并发**: 高效处理大量内容
- **质量控制**: 内置验证和重新生成机制

## 📁 项目结构

```
documents/                     # 新的文档处理工作目录
├── source/                   # 原始文档存储
├── extracted/                # 提取内容
│   └── [项目名]/
│       ├── [文档名].md      # 提取的Markdown内容
│       └── images/          # 提取的图片文件
├── anki/                     # 生成的Anki闪卡文件
├── cache/                    # 处理缓存
│   └── [项目名]/            # 项目专用缓存
└── logs/                     # 处理日志
    └── [项目名].log         # 项目专用日志

原有项目结构（兼容保留）:
├── md_to_anki.py             # 主脚本（流式处理版本）
├── extract_pdf_md.py         # 原PDF转Markdown工具
├── config.ini                # 配置文件
├── requirements.txt          # Python依赖
├── cache/                    # API调用缓存目录
├── tiku_md/                  # 原Markdown文件目录
│   ├── tiku.md              # 题库文件
│   └── images/              # 图片资源
└── test_*.py                 # 测试脚本

新增核心模块:
├── file_manager.py           # 统一的文件命名和路径管理
├── document_processor.py     # 多文档格式统一处理器
├── pdf_extractor.py          # PDF文档专用提取器
├── word_extractor.py         # Word文档专用提取器
└── demo_multi_document.py    # 功能演示和测试脚本
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装基础依赖
pip install -r requirements.txt

# 可选：安装Word文档支持
pip install python-docx

# 可选：安装图片处理增强
pip install Pillow
```

### 2. 配置设置

编辑 `config.ini` 文件：

```ini
[DEFAULT]
# 文档处理设置
BASE_DIR = documents
PDF_TOP_MARGIN = 50.0
PDF_BOTTOM_MARGIN = 50.0

# AI API配置
API_BASE_URL = https://api.siliconflow.cn/v1
API_KEY = your_api_key_here
MODEL_NAME = deepseek-ai/DeepSeek-V3

# 处理参数
CHUNK_SIZE = 500
CHUNK_STRIDE = 450
MAX_WORKERS = 10
REQUEST_TIMEOUT = 120
```

### 3. 新的多文档处理方式

```bash
# 处理单个PDF文档
python document_processor.py document.pdf

# 处理单个Word文档  
python document_processor.py document.docx

# 批量处理目录中的所有文档
python document_processor.py --dir ./docs

# 查看所有处理过的项目
python document_processor.py --list

# 查看处理统计摘要
python document_processor.py --summary

# 运行功能演示
python demo_multi_document.py
```

### 4. 生成Anki闪卡

```bash
# 使用新项目的Markdown文件生成闪卡
# 1. 先处理文档获得项目名称
python document_processor.py mydoc.pdf

# 2. 修改config.ini中的INPUT_FILE指向项目的Markdown文件
# INPUT_FILE = documents/extracted/mydoc_20240614_143052/mydoc.md

# 3. 运行Anki生成
python md_to_anki.py
```

### 5. 传统方式（兼容原有工作流）

```bash
# 使用原有的PDF提取方式
python extract_pdf_md.py input.pdf [output_directory]

# 使用原有的Anki生成方式
python md_to_anki.py
```

## 🔧 使用示例

### 处理单个文档

```python
from document_processor import DocumentProcessor

# 创建处理器
processor = DocumentProcessor(base_dir="my_documents")

# 处理PDF文档
result = processor.process_document("textbook.pdf")
print(f"项目名称: {result['project_name']}")
print(f"提取页数: {result['total_pages']}")
print(f"图片数量: {result['image_count']}")

# 处理Word文档
result = processor.process_document("notes.docx")
print(f"段落数: {result['paragraph_count']}")
print(f"表格数: {result['table_count']}")
```

### 批量处理文档

```python
# 处理目录中的所有文档
results = processor.process_directory("./documents", recursive=True)

# 查看处理结果
success_count = 0
for result in results:
    if 'error' not in result:
        print(f"✅ {result['project_name']}: {result['document_type']}")
        success_count += 1
    else:
        print(f"❌ {result['original_file']}: {result['error']}")

print(f"成功处理: {success_count}/{len(results)}")
```

### 项目管理

```python
# 获取所有项目
projects = processor.file_manager.list_projects()
print(f"共有 {len(projects)} 个项目")

# 获取处理摘要
summary = processor.get_processing_summary()
print(f"总图片数: {summary['total_images']}")
print(f"已生成Anki的项目: {summary['projects_with_anki']}")
```

## 📋 配置说明

### 文档处理配置

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `BASE_DIR` | 文档处理基础目录 | `documents` |
| `PDF_TOP_MARGIN` | PDF顶部边距 | `50.0` |
| `PDF_BOTTOM_MARGIN` | PDF底部边距 | `50.0` |

### Anki生成配置（保持兼容）

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `INPUT_FILE` | 输入的Markdown文件路径 | `tiku_md/tiku.md` |
| `OUTPUT_FILE` | 输出的Anki文件路径 | `ankiflashcards.txt` |
| `LINES_TO_SKIP` | 跳过文件开头的行数 | `13` |
| `CHUNK_SIZE` | 每个文本块的行数 | `500` |
| `CHUNK_STRIDE` | 滑动窗口步长 | `450` |
| `MAX_WORKERS` | 最大并发线程数 | `10` |
| `REQUEST_TIMEOUT` | API请求超时时间(秒) | `120` |

## 🔄 文件命名规范

### 项目命名
- 格式：`{清理后的文件名}_{时间戳}`
- 示例：`textbook_20240614_143052`

### 图片命名  
- 格式：`{项目名}_{来源类型}_{页码}_{图片索引}.png`
- 示例：`textbook_20240614_143052_page_001_img_001.png`

### 缓存文件
- 格式：`chunk_{分块索引:04d}.json`
- 示例：`chunk_0001.json`

## 🎯 支持的文档格式

| 格式 | 扩展名 | 文本提取 | 图片提取 | 表格处理 | 状态 |
|------|--------|----------|----------|----------|------|
| PDF | .pdf | ✅ | ✅ | - | 完整支持 |
| Word | .docx, .doc | ✅ | ✅ | ✅ | 完整支持 |
| 纯文本 | .txt, .md | ✅ | - | - | 基础支持 |

## 📊 支持的AI API提供商

- **SiliconFlow** (`siliconflow`)
- **OpenAI** (`openai`) 
- 其他兼容OpenAI格式的API

## 🐛 故障排除

### 文档处理问题

1. **Word文档处理失败**
   ```bash
   pip install python-docx
   ```

2. **图片提取失败**
   ```bash  
   pip install PyMuPDF Pillow
   ```

3. **文件权限问题**
   - 确保对文档和目标目录有读写权限
   - 检查文档是否被其他程序占用

### Anki生成问题

1. **503 服务器错误**
   - 脚本使用流式处理，已解决大块内容导致的503问题
   - 如仍有问题，可减小 `CHUNK_SIZE` 参数

2. **JSON解析错误**
   - 脚本具备自动修复机制
   - 会尝试正则表达式备用提取

3. **API超时**
   - 增加 `REQUEST_TIMEOUT` 参数
   - 减少 `MAX_WORKERS` 并发数

### 日志查看

```bash
# 查看特定项目的日志
cat documents/logs/project_name.log

# 查看演示日志
cat demo.log

# 查看主程序日志
cat script.log
```

## 🧪 测试和验证

### 运行功能演示

```bash  
python demo_multi_document.py
```

### 运行单元测试

```bash
# 主要功能测试
python test_md_to_anki.py

# API连接测试
python test_api_connection.py

# 流式处理测试
python test_direct_stream.py
```

## 📚 依赖说明

### 必需依赖
- `requests>=2.25.0`: HTTP API调用
- `tqdm>=4.60.0`: 进度条显示
- `PyMuPDF>=1.23.0`: PDF处理
- `configparser>=5.0.0`: 配置文件解析

### 可选依赖
- `python-docx>=0.8.11`: Word文档支持
- `Pillow>=9.0.0`: 增强图片处理

## 🔄 版本说明

### v2.0 多文档处理版本
- ✅ 新增多文档格式支持（PDF、Word）
- ✅ 统一的文件管理和命名系统
- ✅ 项目化的文档组织方式
- ✅ 批量处理和项目管理功能
- ✅ 保持原有功能的完全兼容

### v1.x 传统版本（兼容保留）
- ✅ 流式处理能力
- ✅ 支持 `reasoning_content` 字段
- ✅ JSON自动修复机制
- ✅ 正则表达式备用提取
- ✅ 详细的错误日志

## 🎉 输出格式

生成的Anki文件格式：
```
#separator:tab
#html:true  
#tags column:3
问题内容	答案内容
```

## 🤝 使用建议

1. **新项目推荐**: 使用新的多文档处理系统（`document_processor.py`）
2. **现有项目**: 可继续使用原有工作流，完全兼容
3. **批量处理**: 利用目录处理功能提高效率
4. **项目管理**: 定期查看项目摘要，清理不需要的缓存

---

**注意**：请确保API密钥安全，不要将包含敏感信息的配置文件提交到版本控制系统。
