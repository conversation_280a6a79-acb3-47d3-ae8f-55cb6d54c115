#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
multi_channel_config.py

多通道配置解析和管理模块
"""

import os
import logging
import configparser
from typing import Dict, List, Any
from pathlib import Path


class MultiChannelConfigParser:
    """多通道配置解析器"""
    
    def __init__(self, config_file: str = "config.ini"):
        """
        初始化配置解析器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        # 默认配置
        config = {
            'pdf_top_margin': 50.0,
            'pdf_bottom_margin': 50.0,
            'chunk_size': 500,
            'chunk_stride': 450,
            'max_workers': 10,
            'lines_to_skip': 13,
            'cache_dir': 'cache',
            'channels': []  # 多通道配置
        }
        
        if not os.path.exists(self.config_file):
            logging.warning(f"配置文件 {self.config_file} 不存在，使用默认配置")
            return config
        
        try:
            parser = configparser.ConfigParser()
            parser.read(self.config_file, encoding="utf-8")
            
            # 加载基础配置
            if 'DEFAULT' in parser:
                default_section = parser['DEFAULT']
                config.update({
                    'pdf_top_margin': float(default_section.get('PDF_TOP_MARGIN', 50.0)),
                    'pdf_bottom_margin': float(default_section.get('PDF_BOTTOM_MARGIN', 50.0)),
                    'chunk_size': int(default_section.get('CHUNK_SIZE', 500)),
                    'chunk_stride': int(default_section.get('CHUNK_STRIDE', 450)),
                    'max_workers': int(default_section.get('MAX_WORKERS', 10)),
                    'lines_to_skip': int(default_section.get('LINES_TO_SKIP', 13)),
                    'cache_dir': default_section.get('CACHE_DIR', 'cache'),
                })
            
            # 检查是否有多通道配置
            channels = self._parse_channels(parser)
            if channels:
                config['channels'] = channels
                logging.info(f"加载了 {len(channels)} 个AI通道配置")
            else:
                # 向后兼容：尝试加载单通道配置
                single_channel = self._parse_single_channel(parser)
                if single_channel:
                    config['channels'] = [single_channel]
                    logging.info("加载了单通道配置（向后兼容模式）")
            
            # 环境变量覆盖
            self._apply_env_overrides(config)
            
        except Exception as e:
            logging.error(f"加载配置文件失败: {e}")
            logging.info("使用默认配置")
        
        return config
    
    def _parse_channels(self, parser: configparser.ConfigParser) -> List[Dict[str, Any]]:
        """解析多通道配置"""
        channels = []
        
        # 查找所有CHANNEL_开头的section
        for section_name in parser.sections():
            if section_name.startswith('CHANNEL_'):
                try:
                    section = parser[section_name]
                    
                    # 必需的配置项
                    required_keys = ['API_BASE_URL', 'API_KEY', 'MODEL_NAME']
                    if not all(key in section for key in required_keys):
                        logging.warning(f"通道 {section_name} 缺少必需配置，跳过")
                        continue
                    
                    channel_config = {
                        'name': section_name.lower(),
                        'api_base_url': section.get('API_BASE_URL').rstrip('/'),
                        'api_provider': section.get('API_PROVIDER', 'openai'),
                        'api_key': section.get('API_KEY'),
                        'model_name': section.get('MODEL_NAME'),
                        'request_timeout': int(section.get('REQUEST_TIMEOUT', 120)),
                        'weight': int(section.get('WEIGHT', 1)),
                        'enabled': section.getboolean('ENABLED', True),
                        'max_concurrent': int(section.get('MAX_CONCURRENT', 5))
                    }
                    
                    channels.append(channel_config)
                    logging.debug(f"解析通道配置: {channel_config['name']}")
                    
                except Exception as e:
                    logging.error(f"解析通道 {section_name} 配置失败: {e}")
        
        return channels
    
    def _parse_single_channel(self, parser: configparser.ConfigParser) -> Dict[str, Any]:
        """解析单通道配置（向后兼容）"""
        try:
            if 'DEFAULT' not in parser:
                return None
            
            section = parser['DEFAULT']
            
            # 检查是否有AI相关配置
            if not all(key in section for key in ['API_BASE_URL', 'API_KEY', 'MODEL_NAME']):
                return None
            
            return {
                'name': 'default',
                'api_base_url': section.get('API_BASE_URL').rstrip('/'),
                'api_provider': section.get('API_PROVIDER', 'openai'),
                'api_key': section.get('API_KEY'),
                'model_name': section.get('MODEL_NAME'),
                'request_timeout': int(section.get('REQUEST_TIMEOUT', 120)),
                'weight': 1,
                'enabled': True,
                'max_concurrent': 5
            }
            
        except Exception as e:
            logging.error(f"解析单通道配置失败: {e}")
            return None
    
    def _apply_env_overrides(self, config: Dict[str, Any]):
        """应用环境变量覆盖"""
        # 全局环境变量覆盖
        env_mappings = {
            'MAX_WORKERS': 'max_workers',
            'CHUNK_SIZE': 'chunk_size',
            'CHUNK_STRIDE': 'chunk_stride',
            'CACHE_DIR': 'cache_dir'
        }
        
        for env_key, config_key in env_mappings.items():
            env_val = os.getenv(env_key)
            if env_val:
                if config_key in ['max_workers', 'chunk_size', 'chunk_stride']:
                    config[config_key] = int(env_val)
                else:
                    config[config_key] = env_val
        
        # 通道特定的环境变量覆盖
        for channel in config.get('channels', []):
            channel_name = channel['name'].upper()
            
            # 支持的环境变量格式：CHANNEL_DEFAULT_API_KEY, CHANNEL_1_MODEL_NAME 等
            env_mappings = {
                f'CHANNEL_{channel_name}_API_BASE_URL': 'api_base_url',
                f'CHANNEL_{channel_name}_API_KEY': 'api_key',
                f'CHANNEL_{channel_name}_MODEL_NAME': 'model_name',
                f'CHANNEL_{channel_name}_API_PROVIDER': 'api_provider',
                f'CHANNEL_{channel_name}_REQUEST_TIMEOUT': 'request_timeout',
                f'CHANNEL_{channel_name}_WEIGHT': 'weight',
                f'CHANNEL_{channel_name}_ENABLED': 'enabled',
                f'CHANNEL_{channel_name}_MAX_CONCURRENT': 'max_concurrent'
            }
            
            for env_key, config_key in env_mappings.items():
                env_val = os.getenv(env_key)
                if env_val:
                    if config_key in ['request_timeout', 'weight', 'max_concurrent']:
                        channel[config_key] = int(env_val)
                    elif config_key == 'enabled':
                        channel[config_key] = env_val.lower() in ['true', '1', 'yes', 'on']
                    else:
                        channel[config_key] = env_val
    
    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self.config
    
    def has_ai_channels(self) -> bool:
        """检查是否有可用的AI通道"""
        channels = self.config.get('channels', [])
        return len(channels) > 0 and any(ch.get('enabled', True) for ch in channels)
    
    def get_enabled_channels(self) -> List[Dict[str, Any]]:
        """获取启用的通道列表"""
        return [ch for ch in self.config.get('channels', []) if ch.get('enabled', True)]
    
    def validate_config(self) -> List[str]:
        """验证配置，返回错误信息列表"""
        errors = []
        
        channels = self.config.get('channels', [])
        if not channels:
            errors.append("没有配置任何AI通道")
            return errors
        
        for i, channel in enumerate(channels):
            channel_name = channel.get('name', f'channel_{i}')
            
            # 检查必需字段
            required_fields = ['api_base_url', 'api_key', 'model_name']
            for field in required_fields:
                if not channel.get(field):
                    errors.append(f"通道 {channel_name} 缺少必需字段: {field}")
            
            # 检查URL格式
            api_url = channel.get('api_base_url', '')
            if api_url and not (api_url.startswith('http://') or api_url.startswith('https://')):
                errors.append(f"通道 {channel_name} API_BASE_URL 格式无效: {api_url}")
            
            # 检查数值范围
            timeout = channel.get('request_timeout', 120)
            if not isinstance(timeout, int) or timeout <= 0:
                errors.append(f"通道 {channel_name} REQUEST_TIMEOUT 必须是正整数")
            
            weight = channel.get('weight', 1)
            if not isinstance(weight, int) or weight <= 0:
                errors.append(f"通道 {channel_name} WEIGHT 必须是正整数")
        
        return errors
    
    def create_sample_config(self, output_file: str = "config_sample.ini"):
        """创建示例配置文件"""
        sample_content = """# Multi-Channel AI Service Configuration
# 多通道AI服务配置文件

[DEFAULT]
# Document Processing Settings
# 文档处理设置
BASE_DIR = documents
PDF_TOP_MARGIN = 50.0
PDF_BOTTOM_MARGIN = 50.0

# Processing Settings
# 处理设置
CHUNK_SIZE = 500
CHUNK_STRIDE = 450
MAX_WORKERS = 10
LINES_TO_SKIP = 13

# Cache Settings
# 缓存设置
CACHE_DIR = cache

# File Naming Settings
# 文件命名设置
USE_TIMESTAMP = true
CLEAN_FILENAMES = true

# AI Channel 1 - DeepSeek
# AI通道1 - DeepSeek
[CHANNEL_1]
API_BASE_URL = https://api.deepseek.com/v1
API_PROVIDER = openai
API_KEY = your_deepseek_api_key_here
MODEL_NAME = deepseek-chat
REQUEST_TIMEOUT = 120
WEIGHT = 2
ENABLED = true
MAX_CONCURRENT = 5

# AI Channel 2 - OpenAI
# AI通道2 - OpenAI
[CHANNEL_2]
API_BASE_URL = https://api.openai.com/v1
API_PROVIDER = openai
API_KEY = your_openai_api_key_here
MODEL_NAME = gpt-4
REQUEST_TIMEOUT = 120
WEIGHT = 1
ENABLED = true
MAX_CONCURRENT = 3

# AI Channel 3 - Custom Provider
# AI通道3 - 自定义提供商
[CHANNEL_3]
API_BASE_URL = https://your-custom-api.com/v1
API_PROVIDER = openai
API_KEY = your_custom_api_key_here
MODEL_NAME = custom-model-name
REQUEST_TIMEOUT = 180
WEIGHT = 1
ENABLED = false
MAX_CONCURRENT = 2

# Environment Variable Examples:
# 环境变量示例：
# export CHANNEL_1_API_KEY="your_actual_key"
# export CHANNEL_2_ENABLED="false"
# export MAX_WORKERS="15"
"""
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(sample_content)
            logging.info(f"示例配置文件已创建: {output_file}")
        except Exception as e:
            logging.error(f"创建示例配置文件失败: {e}")


def main():
    """测试配置解析器"""
    logging.basicConfig(level=logging.INFO)
    
    parser = MultiChannelConfigParser()
    config = parser.get_config()
    
    print("配置解析结果:")
    print(f"最大工作进程数: {config['max_workers']}")
    print(f"AI通道数量: {len(config.get('channels', []))}")
    
    for i, channel in enumerate(config.get('channels', []), 1):
        print(f"通道 {i}: {channel['name']} - {channel['model_name']} ({'启用' if channel['enabled'] else '禁用'})")
    
    # 验证配置
    errors = parser.validate_config()
    if errors:
        print("\n配置错误:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("\n配置验证通过")
    
    # 创建示例配置
    parser.create_sample_config()


if __name__ == "__main__":
    main()
