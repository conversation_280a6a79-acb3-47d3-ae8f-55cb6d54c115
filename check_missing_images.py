#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
check_missing_images.py

检查AI生成的JSON文件中是否遗漏了图片信息
将可能有问题的文件导出到CSV
"""

import os
import json
import csv
import re
import logging
from pathlib import Path
from typing import List, Dict, Tuple, Set
import configparser


def load_config(config_path: str = "config.ini") -> dict:
    """加载配置文件"""
    parser = configparser.ConfigParser()
    parser.read(config_path, encoding="utf-8")
    cfg = parser["DEFAULT"]
    
    return {
        "input_file": cfg.get("INPUT_FILE", "tiku_md/tiku.md"),
        "cache_dir": cfg.get("CACHE_DIR", "cache"),
        "lines_to_skip": int(cfg.get("LINES_TO_SKIP", 13)),
        "chunk_size": int(cfg.get("CHUNK_SIZE", 500)),
        "chunk_stride": int(cfg.get("CHUNK_STRIDE", 450)),
    }


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler('check_images.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )


def read_input_lines(path: str, skip: int) -> List[str]:
    """读取输入文件并跳过指定行数"""
    try:
        with open(path, "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        lines = lines[skip:]
        lines = [line.rstrip() for line in lines if line.strip()]
        
        logging.info(f"成功读取文件 {path}，跳过前 {skip} 行，有效行数: {len(lines)}")
        return lines
        
    except FileNotFoundError:
        logging.error(f"输入文件不存在: {path}")
        return []
    except IOError as e:
        logging.error(f"读取输入文件失败: {e}")
        return []


def make_chunks(lines: List[str], size: int, stride: int) -> List[Tuple[int, str]]:
    """创建文本块"""
    chunks = []
    start = 0
    chunk_idx = 0
    
    while start < len(lines):
        end = min(start + size, len(lines))
        chunk_lines = lines[start:end]
        chunk_text = "\n".join(chunk_lines)
        chunks.append((chunk_idx, chunk_text))
        
        if end >= len(lines):
            break
        
        chunk_idx += 1
        start += stride
    
    return chunks


def extract_images_from_text(text: str) -> Set[str]:
    """从文本中提取所有图片文件名"""
    # 匹配 ![alt](image_path) 和 ![alt](images/image_path) 格式
    pattern = r'!\[.*?\]\((?:images/)?([^)]+\.(?:png|jpg|jpeg|gif|bmp|svg))\)'
    matches = re.findall(pattern, text, re.IGNORECASE)
    
    # 也匹配直接的图片路径格式
    pattern2 = r'(?:images/)?([a-zA-Z0-9_\-]+\.(?:png|jpg|jpeg|gif|bmp|svg))'
    matches2 = re.findall(pattern2, text, re.IGNORECASE)
    
    # 合并并去重
    all_images = set(matches + matches2)
    
    # 过滤掉可能的误匹配（比如太短的文件名）
    filtered_images = {img for img in all_images if len(img) > 5}
    
    return filtered_images


def extract_images_from_json(json_data: List[Dict]) -> Set[str]:
    """从JSON数据中提取所有图片文件名"""
    all_images = set()
    
    for item in json_data:
        front = item.get("front", "")
        back = item.get("back", "")
        
        # 从front和back中提取图片
        all_images.update(extract_images_from_text(front))
        all_images.update(extract_images_from_text(back))
    
    return all_images


def check_chunk_for_missing_images(chunk_idx: int, chunk_text: str, json_path: Path, 
                                   all_json_images: Set[str]) -> Dict:
    """检查单个块是否遗漏图片（考虑跨chunk的图片引用）
    
    Args:
        chunk_idx: 块索引
        chunk_text: 块文本内容
        json_path: JSON文件路径
        all_json_images: 所有JSON文件中的图片集合
    """
    result = {
        "chunk_idx": chunk_idx,
        "json_file": str(json_path),
        "has_json": json_path.exists(),
        "chunk_images": set(),
        "json_images": set(),
        "missing_images": set(),
        "has_missing": False,
        "error": None
    }
    
    try:
        # 提取chunk中的图片
        chunk_images = extract_images_from_text(chunk_text)
        result["chunk_images"] = chunk_images
        
        if not json_path.exists():
            if chunk_images:
                # 检查这些图片是否在其他JSON文件中存在
                truly_missing = chunk_images - all_json_images
                if truly_missing:
                    result["has_missing"] = True
                    result["missing_images"] = truly_missing
                    result["error"] = "JSON文件不存在且图片未在其他JSON中找到"
            return result
        
        # 读取JSON文件
        with open(json_path, "r", encoding="utf-8") as f:
            json_data = json.load(f)
        
        # 提取JSON中的图片
        json_images = extract_images_from_json(json_data)
        result["json_images"] = json_images
        
        # 检查是否有遗漏的图片（只有在所有JSON文件中都找不到的图片才算遗漏）
        missing_images = chunk_images - all_json_images
        result["missing_images"] = missing_images
        result["has_missing"] = len(missing_images) > 0
        
        if result["has_missing"]:
            logging.warning(f"块 {chunk_idx} 真正遗漏图片: {missing_images}")
        
    except Exception as e:
        result["error"] = str(e)
        logging.error(f"检查块 {chunk_idx} 时出错: {e}")
    
    return result


def collect_all_json_images(cache_dir: Path, num_chunks: int) -> Set[str]:
    """收集所有JSON文件中的图片"""
    all_json_images = set()
    
    for i in range(num_chunks):
        json_path = cache_dir / f"{i}.json"
        if json_path.exists():
            try:
                with open(json_path, "r", encoding="utf-8") as f:
                    json_data = json.load(f)
                images = extract_images_from_json(json_data)
                all_json_images.update(images)
            except Exception as e:
                logging.warning(f"读取JSON文件 {json_path} 时出错: {e}")
    
    logging.info(f"从所有JSON文件中收集到 {len(all_json_images)} 个唯一图片")
    return all_json_images


def save_problematic_chunks_to_csv(problematic_chunks: List[Dict], csv_path: str):
    """将有问题的块信息保存到CSV文件"""
    try:
        with open(csv_path, "w", newline="", encoding="utf-8") as csvfile:
            fieldnames = [
                "chunk_idx", 
                "json_file", 
                "has_json", 
                "chunk_images_count", 
                "json_images_count", 
                "missing_images_count",
                "chunk_images", 
                "json_images", 
                "missing_images", 
                "error"
            ]
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for chunk_info in problematic_chunks:
                # 转换set为字符串以便CSV存储
                row = {
                    "chunk_idx": chunk_info["chunk_idx"],
                    "json_file": chunk_info["json_file"],
                    "has_json": chunk_info["has_json"],
                    "chunk_images_count": len(chunk_info["chunk_images"]),
                    "json_images_count": len(chunk_info["json_images"]),
                    "missing_images_count": len(chunk_info["missing_images"]),
                    "chunk_images": "; ".join(sorted(chunk_info["chunk_images"])),
                    "json_images": "; ".join(sorted(chunk_info["json_images"])),
                    "missing_images": "; ".join(sorted(chunk_info["missing_images"])),
                    "error": chunk_info["error"] or ""
                }
                writer.writerow(row)
        
        logging.info(f"问题块信息已保存到: {csv_path}")
        
    except Exception as e:
        logging.error(f"保存CSV文件失败: {e}")


def main():
    """主函数"""
    print("=" * 60)
    print("检查AI生成的JSON文件中遗漏的图片（跨chunk检查版本）")
    print("=" * 60)
    
    setup_logging()
    cfg = load_config()
    
    # 读取输入文件
    lines = read_input_lines(cfg["input_file"], cfg["lines_to_skip"])
    if not lines:
        logging.error("无法读取输入文件")
        return
    
    # 创建文本块
    chunks = make_chunks(lines, cfg["chunk_size"], cfg["chunk_stride"])
    logging.info(f"共生成 {len(chunks)} 个文本块")
    
    # 先收集所有JSON文件中的图片
    cache_dir = Path(cfg["cache_dir"])
    logging.info("正在收集所有JSON文件中的图片...")
    all_json_images = collect_all_json_images(cache_dir, len(chunks))
    
    # 检查每个块
    problematic_chunks = []
    total_chunks_with_images = 0
    total_missing_images = 0
    
    logging.info("开始检查各个块...")
    
    for chunk_idx, chunk_text in chunks:
        json_path = cache_dir / f"{chunk_idx}.json"
        result = check_chunk_for_missing_images(chunk_idx, chunk_text, json_path, all_json_images)
        
        # 统计包含图片的块
        if result["chunk_images"]:
            total_chunks_with_images += 1
        
        # 统计遗漏的图片
        if result["has_missing"]:
            problematic_chunks.append(result)
            total_missing_images += len(result["missing_images"])
    
    # 保存结果到CSV
    if problematic_chunks:
        csv_path = "problematic_chunks.csv"
        save_problematic_chunks_to_csv(problematic_chunks, csv_path)
        
        logging.info("=" * 50)
        logging.info("检查完成")
        logging.info(f"总块数: {len(chunks)}")
        logging.info(f"包含图片的块数: {total_chunks_with_images}")
        logging.info(f"真正有问题的块数: {len(problematic_chunks)}")
        logging.info(f"真正遗漏的图片总数: {total_missing_images}")
        logging.info(f"问题详情已保存到: {csv_path}")
        logging.info("=" * 50)
        
        print(f"\n❌ 发现 {len(problematic_chunks)} 个真正有问题的块")
        print(f"📊 真正遗漏 {total_missing_images} 个图片")
        print(f"📄 详细信息已保存到: {csv_path}")
        
    else:
        logging.info("🎉 所有图片都已正确处理，没有发现真正的遗漏")
        print("\n✅ 所有图片都已正确处理，没有发现真正的遗漏")
if __name__ == "__main__":
    main()
