#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
call_ai_api_fixed.py

提取并修复 call_ai_api 函数
"""

import json
import time
import logging
import random
from pathlib import Path
import requests
from requests.exceptions import RequestException, Timeout


def call_ai_api_fixed(chunk: str, cfg: dict, idx: int) -> bool:
    """调用 AI API 提取问答对，并将结果存入缓存（支持流式调用，修复版）"""
    from md_to_anki_fixed import create_ai_prompt
    
    url = cfg["api_base_url"].rstrip("/") + "/chat/completions"
    headers = {
        "Authorization": f"Bearer {cfg['api_key']}",
        "Content-Type": "application/json",
    }
    
    system_prompt, task_prompt = create_ai_prompt()
    
    # 根据 API Provider 动态构建 payload
    if cfg["api_provider"].lower() == "siliconflow":
        # SiliconFlow 使用单个用户消息
        full_prompt = f"{system_prompt}\n\n{task_prompt}\n\n---\n\n{chunk}"
        payload = {
            "model": cfg["model_name"],
            "messages": [{"role": "user", "content": full_prompt}],
            "temperature": 0.3,
            "max_tokens": 4000,
            "stream": True,  # 启用流式响应
        }
    else:  # 默认为 openai 格式
        payload = {
            "model": cfg["model_name"],
            "messages": [
                {"role": "system", "content": system_prompt + "\n" + task_prompt},
                {"role": "user", "content": chunk},
            ],
            "temperature": 0.3,
            "max_tokens": 4000,
            "stream": True,  # 启用流式响应
        }

    max_retries = 3
    base_delay = 1
    
    for attempt in range(max_retries):
        try:
            logging.debug(f"块 {idx} 第 {attempt + 1} 次尝试调用 AI API (流式)")
            
            resp = requests.post(
                url,
                headers=headers,
                json=payload,
                timeout=cfg["request_timeout"],
                stream=True,  # 启用流式响应
            )
            
            if resp.status_code == 200:
                try:
                    # 收集流式响应内容
                    content_parts = []
                    
                    for line in resp.iter_lines():
                        if line:
                            line = line.decode('utf-8')
                            if line.startswith('data: '):
                                data = line[6:]
                                if data == '[DONE]':
                                    break
                                try:
                                    chunk_data = json.loads(data)
                                    if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                                        delta = chunk_data['choices'][0].get('delta', {})
                                        # 检查 content 字段
                                        if 'content' in delta and delta['content'] is not None:
                                            content_parts.append(delta['content'])
                                        # 同时检查 reasoning_content 字段（某些模型使用这个字段）
                                        elif 'reasoning_content' in delta and delta['reasoning_content'] is not None:
                                            content_parts.append(delta['reasoning_content'])
                                except json.JSONDecodeError:
                                    continue
                    
                    # 合并所有内容片段
                    content = ''.join(content_parts).strip()
                    
                    if content:
                        # 尝试清理响应内容（移除可能的markdown标记）
                        if content.startswith("```json"):
                            content = content[7:]
                        if content.endswith("```"):
                            content = content[:-3]
                        content = content.strip()
                        
                        # 尝试修复可能不完整的 JSON
                        if content and not content.endswith('}'):
                            # 如果 JSON 不完整，尝试简单的修复
                            if '"questions"' in content:
                                # 计算已经开始的问题数量
                                question_count = content.count('{"front"')
                                bracket_count = content.count('{') - content.count('}')
                                
                                # 简单的修复：添加缺失的右括号
                                if bracket_count > 0:
                                    content += '}' * bracket_count
                                    logging.warning(f"块 {idx} 自动修复了不完整的 JSON 响应")
                        
                        # 解析 JSON
                        try:
                            parsed = json.loads(content)
                            questions = parsed.get("questions", [])
                            
                            # 验证问题格式
                            valid_questions = []
                            for q in questions:
                                if isinstance(q, dict) and "front" in q and "back" in q:
                                    if q["front"].strip() and q["back"].strip():
                                        valid_questions.append(q)
                            
                            # 保存到缓存
                            cache_path = Path(cfg["cache_dir"]) / f"{idx}.json"
                            with open(cache_path, "w", encoding="utf-8") as f:
                                json.dump(valid_questions, f, ensure_ascii=False, indent=2)
                            
                            logging.debug(f"块 {idx} 成功提取 {len(valid_questions)} 个问答对")
                            return True
                            
                        except json.JSONDecodeError as e:
                            logging.error(f"块 {idx} JSON 解析失败: {e}")
                            logging.error(f"原始响应内容: {content[:500]}...")
                            
                            # 尝试使用正则表达式提取部分内容
                            import re
                            pattern = r'"front":\s*"([^"]*(?:\\"[^"]*)*)"\s*,\s*"back":\s*"([^"]*(?:\\"[^"]*)*)"'
                            matches = re.findall(pattern, content)
                            
                            if matches:
                                logging.info(f"块 {idx} 通过正则表达式找到 {len(matches)} 个可能的问答对")
                                recovered_questions = []
                                for front, back in matches:
                                    if front.strip() and back.strip():
                                        recovered_questions.append({
                                            "front": front.replace('\\"', '"'),
                                            "back": back.replace('\\"', '"')
                                        })
                                
                                if recovered_questions:
                                    # 保存恢复的内容
                                    cache_path = Path(cfg["cache_dir"]) / f"{idx}.json"
                                    with open(cache_path, "w", encoding="utf-8") as f:
                                        json.dump(recovered_questions, f, ensure_ascii=False, indent=2)
                                    
                                    logging.info(f"块 {idx} 成功恢复 {len(recovered_questions)} 个问答对")
                                    return True
                    else:
                        logging.error(f"块 {idx} 流式响应内容为空")
                        
                except Exception as e:
                    logging.error(f"块 {idx} 处理流式响应失败: {e}")
                    
            elif resp.status_code == 429:
                # 速率限制，使用指数退避
                delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                logging.warning(f"块 {idx} 遇到速率限制 (429)，{delay:.2f}s 后重试")
                time.sleep(delay)
                continue
                
            elif 500 <= resp.status_code < 600:
                # 服务器错误，使用指数退避
                delay = base_delay * (2 ** attempt)
                logging.warning(f"块 {idx} 服务器错误 ({resp.status_code})，{delay}s 后重试")
                time.sleep(delay)
                continue
                
            else:
                logging.error(f"块 {idx} API 返回错误状态码 {resp.status_code}: {resp.text[:500]}...")
                break
                
        except Timeout:
            delay = base_delay * (2 ** attempt)
            logging.warning(f"块 {idx} 请求超时，{delay}s 后重试 (第 {attempt + 1}/{max_retries} 次)")
            if attempt < max_retries - 1:
                time.sleep(delay)
            
        except RequestException as e:
            logging.error(f"块 {idx} 网络请求异常: {e}")
            break
            
        except Exception as e:
            logging.error(f"块 {idx} 未知异常: {e}")
            break

    logging.error(f"块 {idx} 处理失败，已尝试 {max_retries} 次")
    return False


# 测试函数
def test_fixed_call():
    """测试修复后的函数"""
    from md_to_anki_fixed import load_config
    
    cfg = load_config()
    test_chunk = """
1. 根据以下图形规律，下一个图形应该是？
![page_1_img_1.png](images/page_1_img_1.png)
A. 上图第一项
B. 上图第二项  
C. 上图第三项
D. 上图第四项
答：C
解：把线条分为上下移动和左右移动，横线从下往上移动1个单位，竖线从左向右移动1个单位（移到最右边就再回到最左边）
"""
    
    success = call_ai_api_fixed(test_chunk, cfg, "test_fixed")
    print(f"测试结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        # 读取结果
        cache_file = Path(cfg["cache_dir"]) / "test_fixed.json"
        if cache_file.exists():
            with open(cache_file, "r", encoding="utf-8") as f:
                result = json.load(f)
            print(f"提取到 {len(result)} 个问答对:")
            for i, qa in enumerate(result, 1):
                print(f"\n问题 {i}:")
                print(f"Front: {qa.get('front', '')}")
                print(f"Back: {qa.get('back', '')}")


if __name__ == "__main__":
    test_fixed_call()
