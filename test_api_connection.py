#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_api_connection.py

测试 API 连接和响应格式
"""

import requests
import json
from md_to_anki_fixed import load_config

def test_simple_api_call():
    """测试简单的 API 调用"""
    print("🔧 测试 API 连接...")
    
    cfg = load_config()
    url = cfg["api_base_url"].rstrip("/") + "/chat/completions"
    headers = {
        "Authorization": f"Bearer {cfg['api_key']}",
        "Content-Type": "application/json",
    }
    
    # 简单的测试消息
    payload = {
        "model": cfg["model_name"],
        "messages": [{"role": "user", "content": "Hello! Please respond with just 'OK'."}],
        "temperature": 0.3,
        "max_tokens": 10,
        "stream": False,  # 先测试非流式
    }
    
    try:
        print(f"📡 发送请求到: {url}")
        print(f"🤖 模型: {cfg['model_name']}")
        
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                resp_json = response.json()
                print("✅ API 连接成功!")
                print("📋 响应格式:")
                print(json.dumps(resp_json, indent=2, ensure_ascii=False))
                
                # 检查响应结构
                if "choices" in resp_json and len(resp_json["choices"]) > 0:
                    content = resp_json["choices"][0].get("message", {}).get("content", "")
                    print(f"💬 AI 回复: {content}")
                    return True
                else:
                    print("⚠️  响应格式异常")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON 解析失败: {e}")
                print(f"原始响应: {response.text[:500]}...")
                return False
        else:
            print(f"❌ API 请求失败: {response.status_code}")
            print(f"错误信息: {response.text[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False


def test_stream_api_call():
    """测试流式 API 调用"""
    print("\n🔧 测试流式 API 调用...")
    
    cfg = load_config()
    url = cfg["api_base_url"].rstrip("/") + "/chat/completions"
    headers = {
        "Authorization": f"Bearer {cfg['api_key']}",
        "Content-Type": "application/json",
    }
    
    # 简单的测试消息
    payload = {
        "model": cfg["model_name"],
        "messages": [{"role": "user", "content": "Please count from 1 to 5."}],
        "temperature": 0.3,
        "max_tokens": 50,
        "stream": True,  # 启用流式
    }
    
    try:
        print(f"📡 发送流式请求到: {url}")
        
        response = requests.post(url, headers=headers, json=payload, timeout=30, stream=True)
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 流式连接成功!")
            print("📋 流式响应:")
            
            content_parts = []
            chunk_count = 0
            
            for line in response.iter_lines():
                if line:
                    chunk_count += 1
                    line = line.decode('utf-8')
                    print(f"Chunk {chunk_count}: {line}")
                    
                    if line.startswith('data: '):
                        data = line[6:]
                        if data == '[DONE]':
                            print("🏁 流式响应结束")
                            break
                        try:
                            chunk_data = json.loads(data)
                            if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                                delta = chunk_data['choices'][0].get('delta', {})
                                if 'content' in delta and delta['content'] is not None:
                                    content_parts.append(delta['content'])
                                    print(f"Content: {delta['content']}")
                        except json.JSONDecodeError as e:
                            print(f"JSON 解析错误: {e}")
                            continue
                    
                    # 限制输出，避免太长
                    if chunk_count > 20:
                        print("... (超过20个块，截断显示)")
                        break
            
            full_content = ''.join(content_parts)
            print(f"💬 完整回复: {full_content}")
            return True
            
        else:
            print(f"❌ 流式请求失败: {response.status_code}")
            print(f"错误信息: {response.text[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ 流式连接失败: {e}")
        return False


def main():
    print("🚀 API 连接测试")
    print("=" * 50)
    
    # 测试基本连接
    basic_success = test_simple_api_call()
    
    # 测试流式连接
    stream_success = test_stream_api_call()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"基本 API 调用: {'✅ 成功' if basic_success else '❌ 失败'}")
    print(f"流式 API 调用: {'✅ 成功' if stream_success else '❌ 失败'}")
    
    if basic_success and stream_success:
        print("🎉 API 连接完全正常!")
    elif basic_success:
        print("⚠️  基本连接正常，流式连接有问题")
    else:
        print("❌ API 连接存在问题")


if __name__ == "__main__":
    main()
