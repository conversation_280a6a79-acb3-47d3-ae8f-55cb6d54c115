#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
multi_channel_ai_service.py

多通道AI服务模块，支持并发处理和负载均衡
"""

import json
import time
import logging
import random
import threading
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from enum import Enum
import requests
from requests.exceptions import RequestException, Timeout

from ai_service import AIService


class ChannelStatus(Enum):
    """通道状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    DISABLED = "disabled"


@dataclass
class ChannelConfig:
    """AI通道配置"""
    name: str
    api_base_url: str
    api_provider: str
    api_key: str
    model_name: str
    request_timeout: int = 120
    weight: int = 1
    enabled: bool = True
    max_concurrent: int = 5


@dataclass
class ChannelStats:
    """通道统计信息"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_response_time: float = 0.0
    last_request_time: Optional[float] = None
    status: ChannelStatus = ChannelStatus.ACTIVE
    error_message: Optional[str] = None

    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests

    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        if self.successful_requests == 0:
            return 0.0
        return self.total_response_time / self.successful_requests


class ChannelManager:
    """AI通道管理器"""
    
    def __init__(self, channels: List[ChannelConfig]):
        """
        初始化通道管理器
        
        Args:
            channels: 通道配置列表
        """
        self.channels = {ch.name: ch for ch in channels}
        self.stats = {ch.name: ChannelStats() for ch in channels}
        self.ai_services = {}
        self.channel_locks = {ch.name: threading.Semaphore(ch.max_concurrent) for ch in channels}
        self._current_channel_index = 0
        self._lock = threading.Lock()
        
        # 初始化AI服务实例
        self._initialize_ai_services()
    
    def _initialize_ai_services(self):
        """初始化AI服务实例"""
        for name, config in self.channels.items():
            if config.enabled:
                try:
                    # 创建配置字典
                    service_config = {
                        'api_base_url': config.api_base_url,
                        'api_provider': config.api_provider,
                        'api_key': config.api_key,
                        'model_name': config.model_name,
                        'request_timeout': config.request_timeout
                    }
                    
                    self.ai_services[name] = AIService(service_config)
                    logging.info(f"通道 {name} AI服务初始化成功")
                    
                except Exception as e:
                    logging.error(f"通道 {name} AI服务初始化失败: {e}")
                    self.stats[name].status = ChannelStatus.ERROR
                    self.stats[name].error_message = str(e)
    
    def get_available_channels(self) -> List[str]:
        """获取可用的通道列表"""
        available = []
        for name, config in self.channels.items():
            if (config.enabled and 
                self.stats[name].status in [ChannelStatus.ACTIVE, ChannelStatus.INACTIVE] and
                name in self.ai_services):
                available.append(name)
        return available
    
    def select_channel(self, strategy: str = "weighted_round_robin") -> Optional[str]:
        """
        选择一个通道
        
        Args:
            strategy: 选择策略 (round_robin, weighted_round_robin, least_loaded)
            
        Returns:
            选中的通道名称，如果没有可用通道则返回None
        """
        available_channels = self.get_available_channels()
        if not available_channels:
            return None
        
        if strategy == "round_robin":
            return self._round_robin_selection(available_channels)
        elif strategy == "weighted_round_robin":
            return self._weighted_round_robin_selection(available_channels)
        elif strategy == "least_loaded":
            return self._least_loaded_selection(available_channels)
        else:
            return available_channels[0]  # 默认返回第一个
    
    def _round_robin_selection(self, channels: List[str]) -> str:
        """轮询选择"""
        with self._lock:
            channel = channels[self._current_channel_index % len(channels)]
            self._current_channel_index += 1
            return channel
    
    def _weighted_round_robin_selection(self, channels: List[str]) -> str:
        """加权轮询选择"""
        # 构建加权列表
        weighted_channels = []
        for channel in channels:
            weight = self.channels[channel].weight
            weighted_channels.extend([channel] * weight)
        
        if not weighted_channels:
            return channels[0]
        
        with self._lock:
            channel = weighted_channels[self._current_channel_index % len(weighted_channels)]
            self._current_channel_index += 1
            return channel
    
    def _least_loaded_selection(self, channels: List[str]) -> str:
        """最少负载选择"""
        # 简单实现：选择成功率最高且平均响应时间最短的通道
        best_channel = channels[0]
        best_score = float('-inf')
        
        for channel in channels:
            stats = self.stats[channel]
            # 计算综合得分：成功率权重0.7，响应时间权重0.3
            success_score = stats.success_rate * 0.7
            time_score = (1.0 / (stats.average_response_time + 1)) * 0.3
            total_score = success_score + time_score
            
            if total_score > best_score:
                best_score = total_score
                best_channel = channel
        
        return best_channel
    
    def update_stats(self, channel_name: str, success: bool, response_time: float, error_msg: str = None):
        """更新通道统计信息"""
        if channel_name not in self.stats:
            return
        
        stats = self.stats[channel_name]
        stats.total_requests += 1
        stats.last_request_time = time.time()
        
        if success:
            stats.successful_requests += 1
            stats.total_response_time += response_time
            stats.status = ChannelStatus.ACTIVE
            stats.error_message = None
        else:
            stats.failed_requests += 1
            if error_msg:
                stats.error_message = error_msg
            
            # 如果连续失败过多，标记为错误状态
            if stats.success_rate < 0.5 and stats.total_requests >= 10:
                stats.status = ChannelStatus.ERROR
    
    def get_stats_summary(self) -> Dict:
        """获取统计摘要"""
        summary = {
            'total_channels': len(self.channels),
            'active_channels': len([s for s in self.stats.values() if s.status == ChannelStatus.ACTIVE]),
            'channels': {}
        }
        
        for name, stats in self.stats.items():
            summary['channels'][name] = {
                'status': stats.status.value,
                'total_requests': stats.total_requests,
                'success_rate': round(stats.success_rate * 100, 2),
                'avg_response_time': round(stats.average_response_time, 2),
                'error_message': stats.error_message
            }
        
        return summary


class MultiChannelAIService:
    """多通道AI服务"""
    
    def __init__(self, config: Dict):
        """
        初始化多通道AI服务
        
        Args:
            config: 配置字典，包含通道配置
        """
        self.config = config
        self.max_workers = config.get('max_workers', 10)
        
        # 解析通道配置
        channels = self._parse_channel_config(config)
        self.channel_manager = ChannelManager(channels)
        
        logging.info(f"多通道AI服务初始化完成，共 {len(channels)} 个通道")
    
    def _parse_channel_config(self, config: Dict) -> List[ChannelConfig]:
        """解析通道配置"""
        channels = []
        
        # 检查是否有多通道配置
        if 'channels' in config:
            # 新的多通道配置格式
            for channel_data in config['channels']:
                channels.append(ChannelConfig(**channel_data))
        else:
            # 向后兼容：单通道配置
            if all(key in config for key in ['api_base_url', 'api_key', 'model_name']):
                channels.append(ChannelConfig(
                    name="default",
                    api_base_url=config['api_base_url'],
                    api_provider=config.get('api_provider', 'openai'),
                    api_key=config['api_key'],
                    model_name=config['model_name'],
                    request_timeout=config.get('request_timeout', 120),
                    weight=1,
                    enabled=True
                ))
        
        return channels
    
    def call_api(self, chunk: str, idx: int, cache_dir: str = None) -> bool:
        """
        调用AI API处理文本块
        
        Args:
            chunk: 文本块内容
            idx: 块索引
            cache_dir: 缓存目录
            
        Returns:
            是否成功处理
        """
        # 选择通道
        channel_name = self.channel_manager.select_channel()
        if not channel_name:
            logging.error(f"块 {idx} 没有可用的AI通道")
            return False
        
        # 获取通道锁
        channel_lock = self.channel_manager.channel_locks[channel_name]
        
        start_time = time.time()
        success = False
        error_msg = None
        
        try:
            # 获取通道访问权限
            with channel_lock:
                ai_service = self.channel_manager.ai_services[channel_name]
                logging.debug(f"块 {idx} 使用通道 {channel_name}")
                
                # 调用AI服务
                success = ai_service.call_api(chunk, idx, cache_dir)
                
        except Exception as e:
            error_msg = str(e)
            logging.error(f"块 {idx} 通道 {channel_name} 调用失败: {e}")
        
        # 更新统计信息
        response_time = time.time() - start_time
        self.channel_manager.update_stats(channel_name, success, response_time, error_msg)
        
        return success
    
    def get_stats(self) -> Dict:
        """获取服务统计信息"""
        return self.channel_manager.get_stats_summary()
