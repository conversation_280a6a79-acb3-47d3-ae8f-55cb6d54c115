#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
anki_generator.py

Anki卡片生成器模块，提供统一的Anki文件生成功能
"""

import json
import logging
import sys
from pathlib import Path
from typing import List, Dict, Tuple, Optional


class AnkiGenerator:
    """Anki卡片生成器类"""
    
    def __init__(self, cache_dir: str = "cache"):
        self.cache_dir = Path(cache_dir)
        
    def load_cached_results(self, num_chunks: int) -> List[Tuple[int, List[Dict[str, str]]]]:
        """从缓存加载所有结果"""
        results = []
        
        successful_loads = 0
        for i in range(num_chunks):
            chunk_cache_file = self.cache_dir / f"{i}.json"
            if chunk_cache_file.exists():
                try:
                    with open(chunk_cache_file, "r", encoding="utf-8") as f:
                        questions = json.load(f)
                    if questions:  # 只添加非空结果
                        results.append((i, questions))
                        successful_loads += 1
                except Exception as e:
                    logging.warning(f"读取缓存文件 {chunk_cache_file} 失败: {e}")
        
        logging.info(f"成功加载 {successful_loads} 个缓存文件")
        return results
    
    def load_all_cached_results(self) -> List[Tuple[int, List[Dict[str, str]]]]:
        """加载所有可用的缓存结果（自动检测数量）"""
        results = []
        successful_loads = 0
        
        # 自动检测最大的chunk索引
        max_chunk_idx = -1
        for json_file in self.cache_dir.glob("*.json"):
            try:
                chunk_idx = int(json_file.stem)
                max_chunk_idx = max(max_chunk_idx, chunk_idx)
            except ValueError:
                continue
        
        if max_chunk_idx == -1:
            logging.warning("没有找到任何JSON缓存文件")
            return results
        
        # 加载所有文件
        for i in range(max_chunk_idx + 1):
            chunk_cache_file = self.cache_dir / f"{i}.json"
            if chunk_cache_file.exists():
                try:
                    with open(chunk_cache_file, "r", encoding="utf-8") as f:
                        questions = json.load(f)
                    if questions:  # 只添加非空结果
                        results.append((i, questions))
                        successful_loads += 1
                except Exception as e:
                    logging.warning(f"读取缓存文件 {chunk_cache_file} 失败: {e}")
        
        logging.info(f"自动加载了 {successful_loads} 个缓存文件（最大索引: {max_chunk_idx}）")
        return results
    
    def dedupe_and_sort(self, results: List[Tuple[int, List[Dict[str, str]]]]) -> List[Dict[str, str]]:
        """按块索引排序并基于 front 去重，保持第一次出现顺序"""
        # 按索引升序排序
        sorted_blocks = sorted(results, key=lambda x: x[0])
        
        seen = set()
        output = []
        total_questions = 0
        
        for block_idx, questions in sorted_blocks:
            block_added = 0
            for qa in questions:
                total_questions += 1
                front = qa.get("front", "").strip()
                back = qa.get("back", "").strip()
                
                if front and back and front not in seen:
                    seen.add(front)
                    output.append({"front": front, "back": back})
                    block_added += 1
            
            logging.debug(f"块 {block_idx}: {len(questions)} 个问题，去重后添加 {block_added} 个")
        
        duplicates = total_questions - len(output)
        logging.info(f"去重完成: 总共 {total_questions} 个问题，去除 {duplicates} 个重复，保留 {len(output)} 个")
        return output
    
    def count_images_in_qas(self, qas: List[Dict[str, str]]) -> Tuple[int, int]:
        """统计问答对中的图片信息
        
        Returns:
            Tuple[int, int]: (包含图片的问题数量, 图片总数)
        """
        import re
        
        questions_with_images = 0
        total_images = 0
        
        # 图片文件名模式
        image_pattern = r'page_\d+_img_\d+\.png'
        
        for qa in qas:
            front = qa.get("front", "")
            back = qa.get("back", "")
            
            # 检查front和back中的图片
            front_images = re.findall(image_pattern, front)
            back_images = re.findall(image_pattern, back)
            
            images_in_qa = len(front_images) + len(back_images)
            if images_in_qa > 0:
                questions_with_images += 1
                total_images += images_in_qa
                
        return questions_with_images, total_images
    
    def write_anki_file(self, qas: List[Dict[str, str]], output_path: str):
        """生成 Anki TSV 文件"""
        try:
            with open(output_path, "w", encoding="utf-8") as f:
                # 写入 Anki 文件头
                f.write("#separator:tab\n")
                f.write("#html:true\n")
                f.write("#tags column:3\n")
                
                # 写入每个问答对
                for qa in qas:
                    front = qa["front"].replace("\n", "<br>")
                    back = qa["back"].replace("\n", "<br>")
                    f.write(f"{front}\t{back}\n")
            
            # 统计图片信息
            questions_with_images, total_images = self.count_images_in_qas(qas)
            
            logging.info(f"Anki 文件已生成: {output_path}")
            logging.info(f"总共 {len(qas)} 张卡片")
            logging.info(f"其中 {questions_with_images} 张卡片包含图片")
            logging.info(f"图片总数: {total_images}")
            
        except IOError as e:
            logging.error(f"写入 Anki 文件失败: {e}")
            sys.exit(1)
    
    def generate_anki_file(self, output_path: str, num_chunks: Optional[int] = None) -> Dict[str, int]:
        """生成Anki文件的完整流程
        
        Args:
            output_path: 输出文件路径
            num_chunks: 期望的chunk数量，如果为None则自动检测
            
        Returns:
            Dict[str, int]: 统计信息
        """
        # 加载缓存结果
        if num_chunks is not None:
            results = self.load_cached_results(num_chunks)
        else:
            results = self.load_all_cached_results()
        
        if not results:
            logging.error("没有任何有效的处理结果，请检查缓存文件")
            return {"total_cards": 0, "questions_with_images": 0, "total_images": 0, "source_chunks": 0}
        
        # 去重并排序
        qas = self.dedupe_and_sort(results)
        
        if not qas:
            logging.error("没有提取到任何问答对")
            return {"total_cards": 0, "questions_with_images": 0, "total_images": 0, "source_chunks": len(results)}
        
        # 生成Anki文件
        self.write_anki_file(qas, output_path)
        
        # 返回统计信息
        questions_with_images, total_images = self.count_images_in_qas(qas)
        
        return {
            "total_cards": len(qas),
            "questions_with_images": questions_with_images,
            "total_images": total_images,
            "source_chunks": len(results)
        }


def main():
    """独立运行时的主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="生成Anki闪卡文件")
    parser.add_argument("--output", "-o", default="ankiflashcards.txt", help="输出文件路径")
    parser.add_argument("--cache-dir", "-c", default="cache", help="缓存目录")
    parser.add_argument("--chunks", "-n", type=int, help="期望的chunk数量（可选）")
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    print("=" * 60)
    print("Anki 闪卡生成器")
    print("=" * 60)
    
    generator = AnkiGenerator(args.cache_dir)
    stats = generator.generate_anki_file(args.output, args.chunks)
    
    print(f"\n✅ 生成完成！")
    print(f"📄 输出文件: {args.output}")
    print(f"📊 统计信息:")
    print(f"   总卡片数: {stats['total_cards']}")
    print(f"   包含图片的卡片: {stats['questions_with_images']}")
    print(f"   图片总数: {stats['total_images']}")
    print(f"   来源chunk数: {stats['source_chunks']}")


if __name__ == "__main__":
    main()
