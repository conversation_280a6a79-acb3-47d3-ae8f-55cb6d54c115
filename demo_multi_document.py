#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
demo_multi_document.py

演示多文档处理系统的使用方法
展示如何处理PDF、Word文档并生成Anki闪卡
"""

import os
import sys
import logging
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from document_processor import DocumentProcessor
from file_manager import FileManager


def setup_demo_logging():
    """设置演示日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler('demo.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def demo_single_document():
    """演示单文档处理"""
    print("\n" + "="*60)
    print("演示1: 单文档处理")
    print("="*60)
    
    # 检查是否有测试文件
    test_pdf = "tiku.pdf"
    if os.path.exists(test_pdf):
        processor = DocumentProcessor(base_dir="demo_documents")
        
        try:
            result = processor.process_document(test_pdf)
            print(f"✅ 成功处理PDF文档:")
            print(f"   项目名称: {result['project_name']}")
            print(f"   总页数: {result.get('total_pages', '未知')}")
            print(f"   图片数: {result.get('image_count', 0)}")
            print(f"   Markdown文件: {result['markdown_file']}")
            print(f"   图片目录: {result['images_dir']}")
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
    else:
        print("⚠️ 未找到测试PDF文件 tiku.pdf，跳过此演示")


def demo_batch_processing():
    """演示批量处理"""
    print("\n" + "="*60)
    print("演示2: 批量文档处理")
    print("="*60)
    
    # 创建一些测试文件的路径（如果存在的话）
    test_files = []
    
    # 查找当前目录中的文档文件
    current_dir = Path('.')
    for ext in ['.pdf', '.docx', '.doc']:
        test_files.extend(current_dir.glob(f"*{ext}"))
    
    if test_files:
        processor = DocumentProcessor(base_dir="demo_documents")
        file_paths = [str(f) for f in test_files]
        
        print(f"找到 {len(file_paths)} 个文档文件:")
        for file_path in file_paths:
            print(f"  - {file_path}")
        
        try:
            results = processor.process_multiple_documents(file_paths)
            
            print(f"\n批量处理结果:")
            success_count = 0
            for result in results:
                if 'error' in result:
                    print(f"❌ {result['original_file']}: {result['error']}")
                else:
                    print(f"✅ {result['project_name']}: {result['document_type'].upper()}")
                    success_count += 1
            
            print(f"\n总结: 成功处理 {success_count}/{len(results)} 个文档")
            
        except Exception as e:
            print(f"❌ 批量处理失败: {e}")
    else:
        print("⚠️ 当前目录未找到任何支持的文档文件，跳过此演示")


def demo_project_management():
    """演示项目管理功能"""
    print("\n" + "="*60)
    print("演示3: 项目管理")
    print("="*60)
    
    processor = DocumentProcessor(base_dir="demo_documents")
    
    # 获取处理摘要
    summary = processor.get_processing_summary()
    
    print(f"项目统计:")
    print(f"  总项目数: {summary['total_projects']}")
    print(f"  包含图片的项目: {summary['projects_with_images']}")
    print(f"  已缓存的项目: {summary['projects_with_cache']}")
    print(f"  已生成Anki的项目: {summary['projects_with_anki']}")
    print(f"  总图片数: {summary['total_images']}")
    
    if summary['projects']:
        print(f"\n项目详情:")
        for project in summary['projects']:
            status_parts = []
            if project['has_images']:
                status_parts.append(f"{project['image_count']}张图片")
            if project['has_cache']:
                status_parts.append("已缓存")
            if project['has_anki']:
                status_parts.append("已生成Anki")
            
            status = f" ({', '.join(status_parts)})" if status_parts else ""
            print(f"  📁 {project['name']}{status}")
            print(f"     Markdown: {project['markdown_file']}")
    else:
        print("  暂无处理过的项目")


def demo_file_naming():
    """演示文件命名管理"""
    print("\n" + "="*60)
    print("演示4: 文件命名管理")
    print("="*60)
    
    file_manager = FileManager("demo_documents")
    
    # 演示各种文件类型的信息获取
    test_files = [
        "测试文档.pdf",
        "My Document.docx",
        "data-file_v2.txt",
        "图片集合 (1).pdf"
    ]
    
    print("文件命名规范化演示:")
    for file_path in test_files:
        file_info = file_manager.get_file_info(file_path)
        project_name = file_manager.generate_project_name(file_info)
        
        print(f"\n原始文件名: {file_path}")
        print(f"  清理后名称: {file_info['clean_name']}")
        print(f"  项目名称: {project_name}")
        print(f"  文件类型: {file_info['type']}")
        
        # 展示路径结构
        if file_info['type'] in ['pdf', 'word']:
            paths = file_manager.get_extraction_paths(file_path)
            print(f"  提取目录: {paths['extraction_dir']}")
            print(f"  Markdown文件: {paths['markdown_file'].name}")
            print(f"  图片目录: {paths['images_dir']}")
            
            # 展示图片命名
            img_name = file_manager.get_image_name(project_name, 1, 1)
            print(f"  图片示例: {img_name}")


def demo_anki_generation():
    """演示Anki闪卡生成"""
    print("\n" + "="*60)
    print("演示5: Anki闪卡生成准备")
    print("="*60)
    
    processor = DocumentProcessor(base_dir="demo_documents")
    projects = processor.file_manager.list_projects()
    
    if projects:
        # 选择第一个项目进行演示
        project = projects[0]
        project_name = project['name']
        
        print(f"为项目 '{project_name}' 准备生成Anki闪卡:")
        
        try:
            anki_config = processor.generate_anki_for_project(project_name)
            
            print(f"✅ 配置生成成功:")
            print(f"   输入文件: {anki_config['input_file']}")
            print(f"   输出文件: {anki_config['output_file']}")
            print(f"   缓存目录: {anki_config['cache_dir']}")
            print(f"   状态: {anki_config['status']}")
            
            print(f"\n💡 要实际生成Anki闪卡，请运行:")
            print(f"   python md_to_anki.py")
            print(f"   并修改config.ini中的INPUT_FILE为: {anki_config['input_file']}")
            
        except Exception as e:
            print(f"❌ 配置生成失败: {e}")
    else:
        print("⚠️ 没有可用的项目，请先处理一些文档")


def main():
    """主演示函数"""
    print("🚀 多文档处理系统演示")
    print("本演示将展示如何使用新的多文档处理系统")
    
    setup_demo_logging()
    
    try:
        # 运行各个演示
        demo_single_document()
        demo_batch_processing()
        demo_project_management()
        demo_file_naming()
        demo_anki_generation()
        
        print("\n" + "="*60)
        print("🎉 演示完成!")
        print("="*60)
        
        print("\n💡 使用提示:")
        print("1. 使用 document_processor.py 处理各种文档")
        print("2. 使用 --list 查看所有项目")
        print("3. 使用 --summary 查看处理统计")
        print("4. 所有文件都按项目组织在 documents/ 目录下")
        print("5. 每个项目都有独立的图片、缓存和日志")
        
        print("\n📖 文档处理命令:")
        print("  python document_processor.py document.pdf      # 处理单个PDF")
        print("  python document_processor.py --dir ./docs      # 处理目录")
        print("  python document_processor.py --list            # 列出项目")
        print("  python document_processor.py --summary         # 显示摘要")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        logging.error(f"演示错误: {e}", exc_info=True)


if __name__ == "__main__":
    main()
