#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_stream_api_fixed.py

使用修复版本测试流式 API 调用
"""

import json
from pathlib import Path
from md_to_anki_fixed import load_config, call_ai_api, read_input_lines, make_chunks


def test_stream_with_real_chunk():
    """使用真实文档的第一个块测试流式 API"""
    print("=" * 60)
    print("测试流式 API 调用 - 使用真实文档第一块 (修复版)")
    print("=" * 60)
    
    # 加载配置
    cfg = load_config()
    print(f"✅ 配置加载成功")
    print(f"API 提供商: {cfg['api_provider']}")
    print(f"API 基础URL: {cfg['api_base_url']}")
    print(f"模型名称: {cfg['model_name']}")
    
    # 创建缓存目录
    cache_path = Path(cfg["cache_dir"])
    cache_path.mkdir(exist_ok=True)
    
    try:
        # 读取输入文件
        print(f"\n📖 读取输入文件: {cfg['input_file']}")
        lines = read_input_lines(cfg["input_file"], cfg["lines_to_skip"])
        print(f"✅ 成功读取 {len(lines)} 行有效内容")
        
        # 创建文本块
        print(f"\n🔄 创建文本块...")
        chunks = make_chunks(lines, cfg["chunk_size"], cfg["chunk_stride"])
        
        if not chunks:
            print("❌ 没有生成任何文本块")
            return False
            
        print(f"✅ 生成了 {len(chunks)} 个文本块")
        
        # 使用第一个块进行测试
        first_chunk_idx, first_chunk_text = chunks[0]
        print(f"\n🧪 测试第一个块 (索引: {first_chunk_idx})")
        print(f"块大小: {len(first_chunk_text)} 字符")
        print(f"块内容预览: {first_chunk_text[:200]}...")
        
        # 清理可能存在的测试缓存
        test_cache_file = cache_path / f"test_stream_{first_chunk_idx}.json"
        if test_cache_file.exists():
            test_cache_file.unlink()
        
        print(f"\n🚀 开始流式 API 调用...")
        
        # 调用流式 API（使用特殊的测试索引）
        test_idx = f"test_stream_{first_chunk_idx}"
        success = call_ai_api(first_chunk_text, cfg, test_idx)
        
        print(f"API 调用结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            # 读取并显示结果
            cache_file = cache_path / f"{test_idx}.json"
            try:
                with open(cache_file, "r", encoding="utf-8") as f:
                    questions = json.load(f)
                
                print(f"\n📊 流式 API 提取结果:")
                print(f"问答对数量: {len(questions)}")
                
                for i, qa in enumerate(questions, 1):
                    print(f"\n--- 问题 {i} ---")
                    print(f"Front: {qa.get('front', '')}")
                    print(f"Back: {qa.get('back', '')}")
                    print("-" * 40)
                
                print(f"\n✅ 缓存文件保存在: {cache_file}")
                
                # 验证结果质量
                validate_results(questions)
                
                return True
                
            except Exception as e:
                print(f"❌ 读取结果失败: {e}")
                return False
        else:
            print(f"❌ 流式 API 调用失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False


def validate_results(questions):
    """验证提取结果的质量"""
    print(f"\n🔍 结果质量验证:")
    
    if not questions:
        print("⚠️  没有提取到任何问答对")
        return
    
    # 统计信息
    total_questions = len(questions)
    questions_with_images = sum(1 for q in questions if "![" in q.get("front", ""))
    questions_with_options = sum(1 for q in questions if any(opt in q.get("front", "") for opt in ["A.", "B.", "C.", "D."]))
    
    print(f"✅ 总计问答对: {total_questions}")
    print(f"✅ 包含图片的问题: {questions_with_images}")
    print(f"✅ 包含选项的问题: {questions_with_options}")
    
    # 检查是否有空内容
    empty_fronts = sum(1 for q in questions if not q.get("front", "").strip())
    empty_backs = sum(1 for q in questions if not q.get("back", "").strip())
    
    if empty_fronts > 0:
        print(f"⚠️  发现 {empty_fronts} 个空问题")
    if empty_backs > 0:
        print(f"⚠️  发现 {empty_backs} 个空答案")
    
    # 检查是否包含营销信息
    marketing_found = sum(1 for q in questions if "jobhelp101" in q.get("front", "") + q.get("back", ""))
    if marketing_found > 0:
        print(f"⚠️  发现 {marketing_found} 个包含营销信息的问答对")
    else:
        print("✅ 成功过滤营销信息")


def main():
    """主测试函数"""
    print("🚀 开始流式 API 测试 (修复版)")
    print("📋 测试目标: 使用真实文档的第一个块测试流式调用")
    print()
    
    success = test_stream_with_real_chunk()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success:
        print("🎉 流式 API 测试成功！")
        print("💡 建议: 流式调用可以处理更大的内容块，减少503错误")
        print("💡 建议: 现在可以运行主脚本 `python md_to_anki_fixed.py` 进行完整处理")
    else:
        print("❌ 流式 API 测试失败")
        print("🔧 请检查:")
        print("  - API 端点是否支持流式调用")
        print("  - API 密钥是否正确")
        print("  - 网络连接是否稳定")
        print("  - 输入文件是否存在且格式正确")


if __name__ == "__main__":
    main()
